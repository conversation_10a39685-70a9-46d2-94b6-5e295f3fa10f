apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "api.fullname" . }}
  labels:
    {{- include "api.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "api.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "api.selectorLabels" . | nindent 8 }}
        date: "{{ now | unixEpoch }}"
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "api.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: {{ include "api.fullname" . }}-mysql-dump
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: data
              mountPath: /backup/
          env:
            - name: ALGOLIA_ADMIN_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: algoliaAdminApiKey
            - name: ALGOLIA_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: algoliaApiKey
            - name: ALGOLIA_APPLICATIONID
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: algoliaApplicationId
            - name: ALGOLIA_SEARCH_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: algoliaSearchApiKey
            - name: API_BASE_URL
              value: "https://{{ .Values.api.hostname }}"
            - name: BACK_OFFICE_BASE_URL
              value: "https://{{ .Values.bo.hostname }}"
            - name: BEETWEEN_JP_REMOTE_URL
              value: {{ .Values.beetween.jpRemoteUrl | quote }}
            - name: BEETWEEN_JOA_SPONTANEOUS_CANDIDATURE_EMAIL
              value: {{ .Values.beetween.joaSpontaneousEmail | default "<EMAIL>" | quote }}
            - name: BEETWEEN_SEND_CANDIDATURES_URL
              value: {{ .Values.beetween.jpSendCandidaturesUrl | quote }}
            - name: CONTAINER_NAME
              value: "<no value>"
            - name: EMAIL_VERIFICATION_API_KEY
              value: {{ .Values.email.verificationApiKey | quote }}
            - name: EOLIA_MB_CANDIDATURE_MAIL
              value: {{ .Values.eolia.mbCandidatureMail | quote }}
            - name: EOLIA_EMALEC_CANDIDATURE_MAIL
              value: {{ .Values.eolia.emalecCandidatureMail | quote }}
            - name: EOLIA_SERFIM_RECRUITER_CODE
              value: "S-21886"
            - name: EOLIA_SERFIM_CANDIDATURE_MAIL
              value: {{ .Values.eolia.serfimCandidatureMail | quote }}
            - name: EOLIA_DELAY_IN_MINUTES
              value: {{ .Values.eolia.delayInMinutes | quote }}
            - name: ADECCO_FORCED_SLACK_CHANNEL
              value: {{ .Values.adecco.forcedSlackChannel | default "C06PA2UG4ES" | quote }}
            - name: ADECCO_API_KEY
              value: {{ .Values.adecco.apiKey | quote }}
            - name: ADECCO_FETCH_URL
              value: {{ .Values.adecco.fetchUrl | quote }}
            - name: ADECCO_SEND_CANDIDATURES_URL
              value: {{ .Values.adecco.candidatureApiUrl | quote }}
            - name: SOURCING_CAPACITY_TOLERANCE
              value: {{ .Values.sourcingCapacityTolerance | quote }}
            - name: ADECCO_NB_DAYS
              value: {{ .Values.adecco.nbDays | quote }}
            {{- if .Values.adecco.departments }}
            - name: ADECCO_DEPARTMENTS
              value: {{ .Values.adecco.departments | quote }}
            {{- end }}
            {{- if .Values.april }}
            - name: DR_APRIL_API_KEY
              value: {{ .Values.april.apiKey | quote }}
            - name: DR_APRIL_SEND_CANDIDATURES_URL
              value: {{ .Values.april.sendCandidaturesUrl | quote }}
            - name: APRIL_NB_DAYS
              value: {{ .Values.april.nbDays | default "" | quote }}
            {{- end }}
            {{- if .Values.foncia }}
            - name: DR_FONCIA_API_KEY
              value: {{ .Values.foncia.apiKey | quote }}
            - name: DR_FONCIA_SEND_CANDIDATURES_URL
              value: {{ .Values.foncia.sendCandidaturesUrl | quote }}
            - name: DR_FONCIA_RECRUITER_CODE
              value: {{ .Values.foncia.recruiterCode | default "S-21679" | quote }}
            {{- end }}
            {{- if .Values.efficity }}
            - name: DR_EFFICITY_API_KEY
              value: {{ .Values.efficity.apiKey | quote }}
            - name: DR_EFFICITY_SEND_CANDIDATURES_URL
              value: {{ .Values.efficity.sendCandidaturesUrl | quote }}
            - name: DR_EFFICITY_RECRUITER_CODE
              value: {{ .Values.efficity.recruiterCode | default "S-21879" | quote }}
            {{- end }}
            {{- if .Values.keolisLyon }}
            - name: DR_KEOLIS_LYON_API_KEY
              value: {{ .Values.keolisLyon.apiKey | quote }}
            - name: DR_KEOLIS_LYON_SEND_CANDIDATURES_URL
              value: {{ .Values.keolisLyon.sendCandidaturesUrl | quote }}
            - name: DR_KEOLIS_LYON_RECRUITER_CODE
              value: {{ .Values.keolisLyon.recruiterCode | default "S-21436" | quote }}
            {{- end }}
            {{- if .Values.cegid }}
            - name: DR_CEGID_API_KEY
              value: {{ .Values.cegid.apiKey | quote }}
            - name: DR_CEGID_SEND_CANDIDATURES_URL
              value: {{ .Values.cegid.sendCandidaturesUrl | quote }}
            {{- end }}
            {{- if .Values.cegid }}
            - name: DR_FONCIA_API_KEY
              value: {{ .Values.foncia.apiKey | quote }}
            - name: DR_FONCIA_SEND_CANDIDATURES_URL
              value: {{ .Values.foncia.sendCandidaturesUrl | quote }}
            - name: DR_FONCIA_RECRUITER_CODE
              value: {{ .Values.foncia.recruiterCode | default "S-21679" | quote }}
            {{- end }}
            - name: HW_BEL_FORCED_EMAIL
              value: {{ .Values.helloWork.sendCandidaturesBelmail | quote }}
            - name: HW_CAF69_FORCED_EMAIL
              value: {{ .Values.helloWork.sendCandidaturesCAF69Email | quote }}
            - name: HW_MLOGISTICS_FORCED_EMAIL
              value: {{ .Values.helloWork.sendCandidaturesEmail | quote }}
            - name: HW_MDA_FORCED_EMAIL
              value: {{ .Values.helloWork.sendCandidaturesMDAEmail | quote }}
            - name: HW_SACVL_FORCED_EMAIL
              value: {{ .Values.helloWork.sendCandidaturesSACVLEmail | quote }}
            - name: TALENT_PLUG_AREAS_FORCED_EMAIL
              value: {{ .Values.talentPlug.sendCandidaturesEmail | quote }}
            - name: CANDIDATUS_ARTELOGE_CANDIDATURE_MAIL
              value: {{ .Values.candidatus.sendCandidaturesEmail | quote }}
            - name: CANDIDATUS_ARTELOGE_SPONTANEOUS_CANDIDATURE_EMAIL
              value: {{ .Values.candidatus.sendSpontaneousCandidaturesEmail | quote }}
            - name: TALENT_PLUG_BOFROST_FORCED_EMAIL
              value: {{ .Values.talentPlug.sendCandidaturesBoFrostEmail | quote }}
            - name: TALENT_PLUG_MARIETTON_FORCED_EMAIL
              value: {{ .Values.talentPlug.sendCandidaturesMariettonEmail | quote }}
            - name: TALENT_PLUG_AREAS_FETCH_URL
              value: {{ .Values.talentPlug.areaFetchUrl | quote }}
            - name: TALENT_PLUG_BOFROST_FETCH_URL
              value: {{ .Values.talentPlug.bofrostFetchUrl | quote }}
            - name: TALENT_PLUG_MARIETTON_REMOTE_URL
              value: {{ .Values.talentPlug.mariettonFetchUrl | quote }}
            - name: FRONT_OFFICE_BASE_URL
              value: "https://{{ .Values.fo.hostname }}"
            {{- if .Values.gestmaxOpteven }}
            {{- if .Values.gestmaxOpteven.remoteUrl }}
            - name: GESTMAX_OPTEVEN_REMOTE_URL
              value: {{ .Values.gestmaxOpteven.remoteUrl | quote }}
            {{- end }}
            {{- if .Values.gestmaxOpteven.authentication }}
            - name: GESTMAX_OPTEVEN_AUTHENTICATION
              value: {{ .Values.gestmaxOpteven.authentication | quote }}
            {{- end }}
            {{- if .Values.gestmaxOpteven.candidatureUrl }}
            - name: GESTMAX_OPTEVEN_CANDIDATURES_URL
              value: {{ .Values.gestmaxOpteven.candidatureUrl | quote }}
            {{- end }}
            {{- end }}
            {{- if .Values.gestmaxElior }}
            {{- if .Values.gestmaxElior.remoteUrl }}
            - name: GESTMAX_ELIORG_REMOTE_URL
              value: {{ .Values.gestmaxElior.remoteUrl | quote }}
            {{- end }}
            {{- if .Values.gestmaxElior.authentication }}
            - name: GESTMAX_ELIORG_AUTHENTICATION
              value: {{ .Values.gestmaxElior.authentication | quote }}
            {{- end }}
            {{- if .Values.gestmaxElior.candidatureUrl }}
            - name: GESTMAX_ELIORG_CANDIDATURES_URL
              value: {{ .Values.gestmaxElior.candidatureUrl | quote }}
            {{- end }}
            {{- if .Values.gestmaxElior.candidatureUrl }}
            - name: GESTMAX_ELIORG_TRACKING_ID
              value: {{ .Values.gestmaxElior.trackingId | quote }}
            {{- end }}
            {{- if .Values.gestmaxElior.recruiterCode }}
            - name: GESTMAX_ELIORG_RECRUITER_CODE
              value: {{ .Values.gestmaxElior.recruiterCode | quote }}
            {{- end }}
            {{- end }}
            - name: INFLUXDB_DB
              value: {{ .Values.influxDb.database | quote }}
            - name: INFLUXDB_PASSWORD
              value: {{ .Values.influxDb.password | quote }}
            - name: INFLUXDB_URL
              value: {{ .Values.influxDb.url | quote }}
            - name: INFLUXDB_USERNAME
              value: {{ .Values.influxDb.username | quote }}
            - name: IN_RECRUITING_AIRVANCE_REMOTE_URL
              value: {{ .Values.inRecruiting.remoteUrl | quote }}
            - name: IN_RECRUITING_AIRVANCE_CANDIDATURES_URL
              value: {{ .Values.inRecruiting.candidatureUrl | quote }}
            - name: IN_RECRUITING_AIRVANCE_API_KEY
              value: {{ .Values.inRecruiting.apiKey | quote }}
            - name: IN_RECRUITING_VINCIFACILITIES_API_KEY
              value: {{ .Values.inRecruiting.vinciFacilitiesApiKey  | default "NO_KEY" | quote }}
            - name: IN_RECRUITING_VINCIFACILITIES_CANDIDATURES_URL
              value: {{ .Values.inRecruiting.candidatureUrl | quote }}
            {{- if .Values.talentSoftGroupama }}
            - name: TALENTSOFT_GROUPAMA_TRACKING_ID
              value: {{.Values.talentSoftGroupama.trackingId | quote }}
            - name: TALENTSOFT_GROUPAMA_CANDIDATURES_URL
              value: {{.Values.talentSoftGroupama.candidaturesUrl | quote }}
            - name: TALENTSOFT_GROUPAMA_DETAIL_URL
              value: {{.Values.talentSoftGroupama.detailUrl | quote }}
            - name: TALENTSOFT_GROUPAMA_REMOTE_URL
              value: {{.Values.talentSoftGroupama.remoteUrl | quote }}
            - name: TALENTSOFT_GROUPAMA_AUTH_URL
              value: {{.Values.talentSoftGroupama.authUrl | quote }}
            - name: TALENTSOFT_GROUPAMA_CLIENT_SECRET
              value: {{.Values.talentSoftGroupama.clientSecret | quote }}
            - name: TALENTSOFT_GROUPAMA_CLIENT_ID
              value: {{.Values.talentSoftGroupama.clientId | quote }}
            {{- end }}
            {{- if .Values.talentSoftMgen }}
            - name: TALENTSOFT_MGEN_TRACKING_ID
              value: {{.Values.talentSoftMgen.trackingId | quote }}
            - name: TALENTSOFT_MGEN_CANDIDATURES_URL
              value: {{.Values.talentSoftMgen.candidaturesUrl | quote }}
            - name: TALENTSOFT_MGEN_DETAIL_URL
              value: {{.Values.talentSoftMgen.detailUrl | quote }}
            - name: TALENTSOFT_MGEN_REMOTE_URL
              value: {{.Values.talentSoftMgen.remoteUrl | quote }}
            - name: TALENTSOFT_MGEN_AUTH_URL
              value: {{.Values.talentSoftMgen.authUrl | quote }}
            - name: TALENTSOFT_MGEN_CLIENT_SECRET
              value: {{.Values.talentSoftMgen.clientSecret | quote }}
            - name: TALENTSOFT_MGEN_CLIENT_ID
              value: {{.Values.talentSoftMgen.clientId | quote }}
            {{- end }}
            {{- if .Values.talentSoftElior }}
            - name: TALENTSOFT_ELIOR_TRACKING_ID
              value: {{.Values.talentSoftElior.trackingId | quote }}
            - name: TALENTSOFT_ELIOR_CANDIDATURES_URL
              value: {{.Values.talentSoftElior.candidaturesUrl | quote }}
            - name: TALENTSOFT_ELIOR_DETAIL_URL
              value: {{.Values.talentSoftElior.detailUrl | quote }}
            - name: TALENTSOFT_ELIOR_REMOTE_URL
              value: {{.Values.talentSoftElior.remoteUrl | quote }}
            - name: TALENTSOFT_ELIOR_AUTH_URL
              value: {{.Values.talentSoftElior.authUrl | quote }}
            - name: TALENTSOFT_ELIOR_CLIENT_SECRET
              value: {{.Values.talentSoftElior.clientSecret | quote }}
            - name: TALENTSOFT_ELIOR_CLIENT_ID
              value: {{.Values.talentSoftElior.clientId | quote }}
            {{- end }}
            - name: INSEE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: inseeApiKey_v2
            - name: FIREBASE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: firebaseApiKey
            - name: GMAIL_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: gmailApiKey
            {{- if .Values.teamTailor }}
            - name: TEAMTAILOR_CANDIDATURES_URL
              value: {{ .Values.teamTailor.candidatureUrl | quote }}
            - name: TEAMTAILOR_AUTHENTICATION
              value: {{ .Values.teamTailor.authentication | quote }}
            {{- end }}
            - name: MOBILE_NOTIFICATION_TESTERS
              value: {{ .Values.mobile.notificationTestUsersId | quote }}
            - name: FORCED_NOTIFICATION_RECIPIENT_USER_ID
              value: {{ .Values.mobile.forcedNotificationRecipientUserId | quote }}
            - name: MOBILE_NOTIFICATION_TEST_CRON
              value: {{ .Values.mobile.notificationTestCron | quote }}
            - name: NOTIFICATION_CLEANUP_RETENTION_MONTHS
              value: {{ .Values.notificationCleanup.retentionInMonths | default "3" | quote }}
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: openaiKey
            - name: FIRECRAWL_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: firecrawlApiKey
            - name: JAVA_OPTS
              value: "-Djdk.tls.client.protocols=TLSv1.2"
            - name: KEYCLOAK_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakAdminPassword
            - name: KEYCLOAK_ADMIN_USERNAME
              value: {{ .Values.keycloak.adminUsername | quote }}
            - name: KEYCLOAK_BACK_API_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakBackApiClientSecret
            - name: KEYCLOAK_BACK_OFFICE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakBackOfficeClientSecret
            - name: KEYCLOAK_DB_HOST
              value: {{ .Values.keycloak.database.host | quote }}
            - name: KEYCLOAK_DB_NAME
              value: {{ .Values.keycloak.database.database | quote }}
            - name: KEYCLOAK_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakDbPassword
            - name: KEYCLOAK_DB_PORT
              value: {{ .Values.keycloak.database.port | quote }}
            - name: KEYCLOAK_DB_USER
              value: {{ .Values.keycloak.database.user | quote }}
            - name: KEYCLOAK_FRONT_API_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakFrontApiClientSecret
            - name: KEYCLOAK_FRONT_OFFICE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakFrontOfficeClientSecret
            - name: KEYCLOAK_REALM
              value: {{ .Values.keycloak.realm | quote }}
            - name: KEYCLOAK_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakSecret
            - name: KEYCLOAK_GOOGLE_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakGoogleClientId
            - name: KEYCLOAK_GOOGLE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakGoogleClientSecret
            - name: KEYCLOAK_APPLE_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakAppleClientId
            - name: KEYCLOAK_APPLE_TEAM_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakAppleTeamId
            - name: KEYCLOAK_APPLE_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakAppleKeyId
            - name: KEYCLOAK_APPLE_P8_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: keycloakAppleP8Key
            - name: KEYCLOAK_URL
              value: {{ .Values.keycloak.url | quote }}
            - name: MYSQL_DATABASE
              value: {{ .Values.mariadb.auth.database | quote }}
            - name: MYSQL_PASSWORD
              # TODO: Use secret
              value: {{ .Values.mariadb.auth.password | quote }}
            - name: MYSQL_PORT
              value: "3306"
            - name: MYSQL_URL
              value: {{ .Release.Name }}-mariadb
            - name: MYSQL_USER
              value: {{ .Values.mariadb.auth.username | quote }}
            - name: POLE-EMPLOI_CLIENT-ID
              value: {{ .Values.poleEmploi.clientId | quote }}
            - name: POLE-EMPLOI_CLIENT-SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: poleEmploiClientSecret
            - name: POLE-EMPLOI_GRANT-TYPE
              value: {{ .Values.poleEmploi.grantType | quote }}
            - name: POLE-EMPLOI_REALM
              value: {{ .Values.poleEmploi.realm | quote }}
            - name: POLE-EMPLOI_SCOPE
              value: {{ .Values.poleEmploi.scope | quote }}
            - name: SENDINBLUE_ALL_FRONT_USERS_LIST_ID
              value: {{ .Values.sendInBlue.listId.allFrontUsers | quote }}
            - name: SENDINBLUE_CANDIDATURE_NOTIFICATION_TEMPLATE_ID
              value: {{ .Values.sendInBlue.templateId.candidatureNotification | quote }}
            - name: SENDINBLUE_CANDIDATURE_PROPOSAL_TEMPLATE_ID
              value: {{ .Values.sendInBlue.templateId.candidatureProposal | quote }}
            - name: SENDINBLUE_SOURCING_CANDIDATURE_PROPOSAL_TEMPLATE_ID
              value: {{ .Values.sendInBlue.templateId.candidatureProposal | quote }}
            - name: SENDINBLUE_CONFIRM_ACCOUNT_TEMPLATE_ID
              value: {{ .Values.sendInBlue.templateId.confirmAccount | quote }}
            - name: SENDINBLUE_CONFIRM_ACCOUNT_FO_TEMPLATE_ID
              value: {{ .Values.sendInBlue.templateId.confirmAccountFo | quote }}
            - name: SENDINBLUE_JOB_DATING_LYON_LIST_ID
              value: {{ .Values.sendInBlue.listId.jobDatingLyon | quote }}
            - name: SENDINBLUE_NOT_ENOUGH_EXPERIENCE_TEMPLATE_IDS
              value: {{ .Values.sendInBlue.templateId.notEnoughExperience | quote }}
            - name: SENDINBLUE_NO_EXPERIENCE_TEMPLATE_IDS
              value: {{ .Values.sendInBlue.templateId.noExperience | quote }}
            - name: SENDINBLUE_OPTIN_FRONT_USERS_LIST_ID
              value: {{ .Values.sendInBlue.listId.optinFrontUsers | quote }}
            - name: SENDINBLUE_DEFAULT_NEW_USER_LIST_ID
              value: {{ .Values.sendInBlue.listId.defaultNewUser | quote }}
            - name: SENDIN_BLUE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: sendInBlueApiKey
            - name: SLACK_USER_NOTIFICATION_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: slackUserNotificationUrl
            - name: SLACK_TOKEN
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: slackToken
            - name: SMTP_ENABLE_AUTH
              value: {{ .Values.smtp.enableAuth | quote }}
            - name: SMTP_ENABLE_SSL
              value: {{ .Values.smtp.enableSsl | quote }}
            - name: SMTP_ENABLE_STARTTLS
              value: {{ .Values.smtp.enableStartTls | quote }}
            - name: SMTP_HOST
              value: {{ .Values.smtp.host | quote }}
            - name: SMTP_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "api.fullname" . }}
                  key: smtpPassword
            - name: SMTP_PORT
              value: {{ .Values.smtp.port | quote }}
            - name: SMTP_USER
              value: {{ .Values.smtp.username | quote }}
            - name: SOCIAL_LOGO_URL
              value: {{ .Values.socialLogoUrl | default "https://jenesuispasuncv.fr/wp-content/uploads/2020/09/bloc-marque-je-ne-suis-pas-un-cv.png" | quote }}
            - name: SOFTY_API_KEY
              value: {{ .Values.softy.apiKey | quote }}
            - name: SOFTY_SEND_CANDIDATURES_URL
              value: {{ .Values.softy.candidatureApiUrl | quote }}

            {{- if .Values.stef }}
            - name: STEF_REQUIRES_CONFIRMATION
              value: {{ .Values.stef.requiresConfirmation | default "false" | quote }}
            - name: STEF_RECRUITER_CODE
              value: {{ .Values.stef.recruiterCode | default "S-21997" | quote }}
            - name: STEF_CANDIDATURE_MAIL
              value: {{ .Values.stef.candidatureMail | default "<EMAIL>" | quote }}
            - name: STEF_ENABLED
              value: {{ .Values.stef.enabled | quote }}
            {{- end }}

            {{- if .Values.covea }}
            - name: COVEA_REMOTE_URL
              value: {{ .Values.covea.ftpUrl | quote }}
            - name: COVEA_FTP_CLIENT_ID
              value: {{ .Values.covea.ftpClientId | quote }}
            - name: COVEA_FTP_SECRET
              value: {{ .Values.covea.ftpSecret | quote }}
            - name: COVEA_CANDIDATURES_URL
              value: {{ .Values.covea.candidatureUrl | quote }}
            - name: COVEA_API_AUTH_URL
              value: {{ .Values.covea.authUrl | quote }}
            - name: COVEA_API_CLIENT_ID
              value: {{ .Values.covea.apiClientId | quote }}
            - name: COVEA_API_CLIENT_SECRET
              value: {{ .Values.covea.apiClientSecret | quote }}
            - name: COVEA_ENABLED
              value: {{ .Values.covea.enabled | default "true" | quote }}
            {{- end }}
            - name: SPRING_OUTPUT_ANSI_ENABLED
              value: "NEVER"
            - name: SPRING_PROFILES_ACTIVE
              value: "master"
              value: {{ .Values.spring.profileActive | quote }}
            - name: graylogHost
              value: {{ .Values.graylog.host | quote }}
            - name: HZ_NETWORK_JOIN_AUTODETECTION_ENABLED
              value: "false"
            - name: HZ_NETWORK_JOIN_MULTICAST_ENABLED
              value: "false"
            - name: HZ_NETWORK_JOIN_KUBERNETES_ENABLED
              value: "true"
            - name: HZ_NETWORK_JOIN_KUBERNETES_SERVICEPORT
              value: "5701"
            - name: HZ_NETWORK_JOIN_KUBERNETES_SERVICENAME
              value:  {{ .Values.kubernetes.serviceName | quote }}
            - name: HZ_INSTANCENAME
              value:  {{ .Values.kubernetes.serviceName | quote }}
            - name: HZ_CLUSTERNAME
              value:  {{ .Values.kubernetes.serviceName | quote }}
            {{- if .Values.taleez }}
            - name: TALEEZ_SEND_CANDIDATURES_URL
              value:  {{ .Values.taleez.candidatureApiUrl | quote }}
            - name: TALEEZ_API_KEY
              value:  {{ .Values.taleez.apiKey | quote }}
            {{- end }}
            - name : TRIMOJI_JSON_TEMPLATE
              value:  {{ .Values.trimoji.requestJsonTemplate | toJson | quote }}
            - name : TRIMOJI_API_KEY
              value:  {{ .Values.trimoji.apiKey | quote }}
            {{- if .Values.debug }}
            - name: javax.net.debug
              value: all
            {{- end }}
            - name: jdk.tls.client.protocols
              value: TLSv1.2
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
            - name: hazelcast
              containerPort: 5701
              protocol: TCP
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: http
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
            successThreshold: {{ .Values.livenessProbe.successThreshold}}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
          {{- end }}
          {{- if .Values.startupProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: http
            failureThreshold: {{ .Values.startupProbe.failureThreshold }}
            successThreshold: {{ .Values.startupProbe.successThreshold}}
            initialDelaySeconds: {{ .Values.startupProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.startupProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.startupProbe.timeoutSeconds }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: http
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
            successThreshold: {{ .Values.readinessProbe.successThreshold}}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
