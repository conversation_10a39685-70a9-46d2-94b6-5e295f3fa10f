# Default values for api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: docker.io/erhgo/api
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: [ ]
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname
  # template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  className: ""
  annotations: { }
    # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: [ ]
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: 2
    memory: 8Gi
  requests:
    cpu: 10m
    memory: 600Mi
  # We usually recommend not to specify default resources and to leave this as a
  # conscious choice for the user. This also increases chances charts run on
  # environments with little resources, such as Minikube. If you do want to
  # specify resources, uncomment the following lines, adjust them as necessary,
  # and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi
cronjob:
  mariadbOptimize:
    resources:
      limits:
        cpu: 2
        memory: 512Mi
      requests:
        cpu: 10m
        memory: 100Mi
backup:
  persistence:
    accessModes:
      - ReadWriteMany
    size: 20Gi
    storageClassName: backup
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: { }

tolerations: [ ]

affinity: { }
adecco:
  nbDays: 5
  apiKey: 42
  candidatureApiUrl: https://erhgo.wiremockapi.cloud
  fetchUrl: https://cd-adecco-fr.uat.cms.adecco.com/resultats-offres-emploi/c-cdd/d-rh%C3%B4ne?buname=adecco.fr%7cadeccopme.fr%7c&rss=1&employmenttype=ADCFREMP004
api:
  hostname: api.erhgo.local
bo:
  hostname: bo.erhgo.local
fo:
  hostname: fo.erhgo.local
beetween:
  jpRemoteUrl: ""
  jpSendCandidaturesUrl: ""
algolia:
  adminApiKey: dummy
  apiKey: dummy
  applicationId: dummy
  searchApiKey: dummy
beetween:
  jpRemoteUrl: ""
email:
  verificationApiKey: dummy
eolia:
  mbCandidatureMail: ""
  emalecCandidatureMail: ""
  serfimCandidatureMail: ""
  delayInMinutes: 60
helloWork:
  sendCandidaturesEmail: "<EMAIL>"
  sendCandidaturesMDAEmail: "<EMAIL>"
  sendCandidaturesSACVLEmail: "<EMAIL>"
  sendCandidaturesCAF69Email: "<EMAIL>"
  sendCandidaturesBelEmail: "<EMAIL>"
inRecruiting:
  apiKey: 42;42
  candidatureUrl: https://erhgo.wiremockapi.cloud
  remoteUrl: https://inrecruitingfr.intervieweb.it/annunci.php?lang=fr&LAC=franceair&d=france-air.com&k=53b01bdff4eeff856a85b0a48bbfc806&format=json_en&utype=0
talentPlug:
  sendCandidaturesEmail: "<EMAIL>"
  sendCandidaturesBoFrostEmail: "<EMAIL>"
  sendCandidaturesMariettonEmail: "<EMAIL>"
  areaFetchUrl: https://www.mytalentplug.com/xml.aspx?jbID=86k1UuIvziM%3D&mid=wFB1SHaPgSM%3D
  bofrostFetchUrl: https://www.mytalentplug.com/xml.aspx?jbID=86k1UuIvziM%3D&mid=2X4CDY9gnYQ%3D
  mariettonFetchUrl: https://www.mytalentplug.com/xml.aspx?jbID=86k1UuIvziM%3D&mid=3mdQ8Jwcth8%3D
candidatus:
  sendCandidaturesEmail: " <EMAIL>"
  sendSpontaneousCandidaturesEmail: " <EMAIL>"
influxDb:
  database: erhgo_local
  password: erhgo
  url: https://dummy-influxdb:8087
  username: erhgo
java:
  opts: "-Djdk.tls.client.protocols=TLSv1.2"
keycloak:
  url: https://auth.erhgo.local
  adminUsername: "admin"
  adminPassword: "erhgo"
  backApiClientSecret: dummy
  backOfficeClientSecret: dummy
  frontApiClientSecret: dummy
  frontOfficeClientSecret: dummy
  realm: erhgo.local.app
  secret: dummy
  googleClientId: dummy
  googleClientSecret: dummy
  appleClientId: dummy
  appleTeamId: dummy
  appleKeyId: dummy
  appleP8Key: dummy
  database:
    host: local-postgresql
    database: keycloak
    username: keycloak
    password: keycloak
    port: '5432'
poleEmploi:
  clientId: dummy
  clientSecret: dummy
  grantType: client_credentials
  realm: /partenaire
  scope: dummy
sendInBlue:
  listId:
    allFrontUsers: 66
    jobDatingLyon: 84
    optinFrontUsers: 50
    defaultNewUser: 346
  templateId:
    candidatureNotification: 130
    candidatureProposal: 156
    confirmAccount: 127
    notEnoughExperience: ",77,77,77"
    noExperience: ",76,76,76"
    confirmAccountFo: 181
  apiKey: ""
slack:
  userNotificationUrl: https://hooks.slack.com/services/dummy
softy:
  apiKey: ""
  candidatureApiUrl: ""
insee:
  apiKey: ""
firebase:
  apiKey: ""
gmail:
  apiKey: ""
mobile:
  notificationTestUsersId: "********-cf61-4885-beae-e6c24a7ef6a9,cae1c269-acbf-405c-8f68-cd47390f7a4d"
  notificationTestCron: 0 0 11 * * MON
  forcedNotificationRecipientUserId: cae1c269-acbf-405c-8f68-cd47390f7a4d
notificationCleanup:
  retentionInMonths: 3
openai:
  apiKey: ""
smtp:
  enableAuth: true
  enableSsl: true
  enableStartTls: true
  host: smtp-relay.brevo.com
  username: dummy
  password: dummy
  port: 465
spring:
  profileActive: master
debug: true
graylog:
  host: dummy
mariadb:
  enabled: true
  auth:
    rootPassword: erhgo
    database: erhgo
    username: erhgo
    password: erhgo
  primary:
    resources:
      requests:
        memory: 800Mi
        cpu: 10m
      limits:
        memory: 2Gi
        cpu: 1
kubernetes:
  serviceName: dummy
readinessProbe:
  enabled: true
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 10
  successThreshold: 1
startupProbe:
  enabled: true
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 10
  successThreshold: 1
livenessProbe:
  enabled: true
  initialDelaySeconds: 180
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 3
  successThreshold: 1
sourcingCapacityTolerance: 0.7
trimoji:
  apiKey: 49aa5835-a09d-4d2e-bde0-44ff99440fcc
  requestJsonTemplate:
    {
      "candidate_email": "%USER_EMAIL%",
      "candidate_first_name": "%USER_FIRSTNAME%",
      "candidate_last_name": "%USER_LASTNAME%",
      "customer_token": "ac4f4825-4d22-43c8-a685-5cb7ab165e18",
      "is_trimoji_sending": false,
      "callback_url": "%CALLBACK_URL%",
      "no_result": false,
      "test_duration": "short",
      "metadatas": {
        "candidate_id": "%USER_ID%"
      }
    }
covea:
  ftpUrl: ftp.covea.csod.com
  ftpClientId: covea_jenesuispasuncv
  ftpSecret: 2kZGO6YG
  candidatureUrl: https://erhgo.wiremockapi.cloud
  authUrl: https://covea-pilot.csod.com/services/api/oauth2/token
  apiClientId: 1limm71b3iwp5
  apiClientSecret: 2f79c3b8a152ea46a94c15456c32a79c10296d8caef481123e6a3e0a9ef40113
  enabled: false
firecrawl:
  apiKey: dummy
  apiUrl: https://api.firecrawl.dev/v1/scrape
stef:
  enabled: false
estMetropole:
  enabled: false
  recruiterCode: "S-22007"
  candidatureMail: "<EMAIL>"
apicil:
  enabled: false
  recruiterCode: "S-22006"
  candidatureMail: "<EMAIL>"
