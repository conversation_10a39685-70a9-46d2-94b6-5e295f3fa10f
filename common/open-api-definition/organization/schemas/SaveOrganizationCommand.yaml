type: object
required:
  - title
  - organizationType
properties:
  id:
    type: integer
    format: int64
  title:
    type: string
    minLength: 1
  description:
    type: string
  siren:
    type: string
  siret:
    type: string
  address:
    type: string
  organizationType:
    $ref: './OrganizationType.yaml'
  refererRecruiterCode:
    type: string
  projectIds:
    type: array
    items:
      type: integer
      format: int64
  defaultProjectId:
    type: integer
    format: int64
  consortiumIds:
    type: array
    items:
      type: integer
      format: int64
  privateUsers:
    type: boolean
  privateJobs:
    type: boolean
  internal:
    type: boolean
  mandatoryIdentity:
    type: boolean
  refusalEmailTemplate:
    $ref: '../../misc/schemas/CustomEmailTemplate.yaml'
  forcedUrl:
    type: string
  defaultDiffusionType:
    $ref: './DiffusionType.yaml'
