type: object
required:
  - id
  - title
  - organizationType
properties:
  id:
    type: integer
    format: int64
  code:
    type: string
  title:
    type: string
    minLength: 1
  organizationType:
    $ref: './OrganizationType.yaml'
  privateUsers:
    type: boolean
  defaultProject:
    $ref: './OrganizationSummary.yaml'
  externalUrl:
    type: string
  defaultDiffusionType:
    $ref: './DiffusionType.yaml'
