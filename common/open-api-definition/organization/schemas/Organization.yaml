type: object
required:
  - id
  - title
  - organizationType
properties:
  id:
    type: integer
    format: int64
  code:
    type: string
  title:
    type: string
    minLength: 1
  description:
    type: string
  siren:
    type: string
  siret:
    type: string
  address:
    type: string
  refererRecruiter:
    $ref: './OrganizationSummary.yaml'
  organizationType:
    $ref: './OrganizationType.yaml'
  projects:
    type: array
    items:
      $ref: './OrganizationSummary.yaml'
  defaultProject:
    $ref: './OrganizationSummary.yaml'
  consortiums:
    type: array
    items:
      $ref: './OrganizationSummary.yaml'
  privateUsers:
    type: boolean
  privateJobs:
    type: boolean
  internal:
    type: boolean
  mandatoryIdentity:
    type: boolean
  refusalEmailTemplate:
    $ref: '../../misc/schemas/CustomEmailTemplate.yaml'
  forcedUrl:
    type: string
    maxLength: 255
  defaultDiffusionType:
    $ref: './DiffusionType.yaml'
  externalUrl:
    type: string
    maxLength: 255
