type: object
title: CreateOrUpdateFullRecruitmentCommand
required:
  - description
  - organizationDescription
  - title
  - jobLocation
  - typeContractCategory
  - workingTimeType
  - baseSalary
  - maxSalary
  - workingWeeklyTime
  - modularWorkingTime
  - criteriaValues
properties:
  recruitmentId:
    type: integer
    format: int64
  description:
    type: string
  organizationDescription:
    type: string
  jobOccupationId:
    type: string
    format: uuid
  title:
    type: string
  jobLocation:
    $ref: '../../misc/schemas/Location.yaml'
  typeContractCategory:
    $ref: ../../job/job/schemas/TypeContractCategory.yaml
  workingTimeType:
    $ref: ../../job/job/schemas/WorkingTime.yaml
  baseSalary:
    type: integer
  maxSalary:
    type: integer
  workingWeeklyTime:
    type: integer
  modularWorkingTime:
    type: boolean
  hideSalary:
    type: boolean
  criteriaValues:
    type: array
    items:
      type: string
  externalOfferId:
    type: string
    format: uuid
  workContractDuration:
    type: integer
  workContractDurationUnit:
    $ref: ../../recruitment/schemas/WorkContractDurationUnit.yaml
  startingDate:
    type: string
    format: date-time
  diffusionType:
    $ref: '../../organization/schemas/DiffusionType.yaml'
