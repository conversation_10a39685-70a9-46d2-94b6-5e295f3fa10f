summary: Get a detail of recruitment for matching purpose
operationId: getRecruitmentForMatching
parameters:
  - $ref: '../../parameters/path/RecruitmentId.yaml'
responses:
  200:
    description: Detail of recruitment
    content:
      application/json:
        schema:
          title: RecruitmentForMatching
          type: object
          required:
            - id
            - code
            - jobId
            - title
            - profileTitle
            - jobTitle
            - mandatoryActivities
            - mandatoryContexts
            - optionalActivities
            - optionalContexts
            - state
            - missions
          properties:
            id:
              type: integer
              format: int64
            code:
              type: string
            diffusionType:
              $ref: '../../organization/schemas/DiffusionType.yaml'
            defaultDiffusionType:
              $ref: '../../organization/schemas/DiffusionType.yaml'
            jobId:
              type: string
              format: uuid
            title:
              type: string
            profileTitle:
              type: string
            jobTitle:
              type: string
            mandatoryActivities:
              type: array
              items:
                $ref: '../../referential/activity/schemas/ActivityLabelSummary.yaml'
            mandatoryContexts:
              type: array
              items:
                $ref: '../../referential/context/schemas/ContextSummary.yaml'
            optionalActivities:
              type: array
              items:
                type: object
                title: OptionalActivity
                properties:
                  activity:
                    $ref: '../../referential/activity/schemas/ActivityLabelSummary.yaml'
                  acquisitionModality:
                    $ref: '../../referential/activity/schemas/AcquisitionModality.yaml'
            optionalContexts:
              type: array
              items:
                type: object
                title: OptionalContext
                properties:
                  context:
                    $ref: '../../referential/context/schemas/ContextSummary.yaml'
                  acquisitionModality:
                    $ref: '../../referential/activity/schemas/AcquisitionModality.yaml'
            state:
              $ref: '../schemas/RecruitmentState.yaml'
            missions:
              type: array
              items:
                title: MissionSummary
                type: object
                required:
                  - id
                  - title
                properties:
                  id:
                    type: integer
                    format: int64
                  title:
                    type: string
