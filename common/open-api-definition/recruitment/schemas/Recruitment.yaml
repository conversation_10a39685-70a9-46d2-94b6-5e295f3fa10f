type: object
required:
  - id
  - code
  - typeContract
properties:
  id:
    type: integer
    format: int64
  code:
    type: string
  typeContract:
    $ref: './TypeContract.yaml'
  workContractDuration:
    type: integer
    format: int32
  workContractDurationUnit:
    $ref: './WorkContractDurationUnit.yaml'
  workingWeeklyTime:
    type: integer
    format: int32
  location:
    $ref: '../../misc/schemas/Location.yaml'
  baseSalary:
    type: integer
    format: int32
  maxSalary:
    type: integer
    format: int32
  hideSalary:
    type: boolean
  externalUrl:
    type: string
  state:
    $ref: './RecruitmentState.yaml'
  startingDate:
    type: string
    format: date-time
  organizationCode:
    type: string
  organizationName:
    type: string
  organizationDescription:
    type: string
  title:
    type: string
  description:
    type: string
  recruitmentProfile:
    $ref: './RecruitmentProfileSummary.yaml'
  job:
    $ref: '../../job/job/schemas/JobSummary.yaml'
  usersIdToNotify:
    type: array
    items:
      type: string
  erhgoClassifications:
    type: array
    items:
      $ref: '../../classifications/erhgo/schemas/ErhgoClassification.yaml'
  diffusionType:
    $ref: '../../organization/schemas/DiffusionType.yaml'
  effectiveDiffusionType:
    $ref: '../../organization/schemas/DiffusionType.yaml'
