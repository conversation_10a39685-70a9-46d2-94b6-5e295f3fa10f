type: object
required:
  - code
  - id
  - jobTitle
  - organizationName
  - classifications
  - criteriaValues
properties:
  state:
    $ref: './RecruitmentState.yaml'
  code:
    type: string
  id:
    type: integer
    format: int64
  jobTitle:
    type: string
  organizationName:
    type: string
  city:
    type: string
  zipCode:
    type: string
  publicationDate:
    type: string
    format: date-time
  publicationEndDate:
    type: string
    format: date-time
  typeContract:
    $ref: './TypeContract.yaml'
  workContractDuration:
    type: integer
  workContractDurationUnit:
    $ref: './WorkContractDurationUnit.yaml'
  workingTimes:
    type: array
    items:
      $ref: '../../job/job/schemas/WorkingTime.yaml'
  workingWeeklyTime:
    type: integer
  baseSalary:
    type: integer
  maxSalary:
    type: integer
  externalUrl:
    type: string
  description:
    type: string
  organizationDescription:
    type: string
  radiusInKm:
    type: integer
  startingDate:
    type: string
    format: date-time
  classifications:
    type: array
    items:
      $ref: '../../classifications/erhgo/schemas/ErhgoClassification.yaml'
  occupation:
    $ref: '../../classifications/erhgo-occupation/schemas/ErhgoOccupationDetail.yaml'
  criteriaValues:
    type: array
    items:
      $ref: '../../criteria/schemas/CriteriaValue.yaml'
