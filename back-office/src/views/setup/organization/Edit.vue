<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-form ref="form">
          <v-card>
            <v-card-title>
              <h2>
                <v-icon large
                        right>mdi-office-building
                </v-icon>
                <template v-if="!isCreation">
                  <small>{{ $t(`ref.${isEmployer ? 'employer' : 'organization'}.edit`) }}</small> :
                  <b>{{ title }}</b>
                </template>
                <template v-else>
                  <b>{{ $t(`ref.${isEmployer ? 'employer' : 'organization'}.create`) }}</b>
                </template>

              </h2>
            </v-card-title>
            <v-card-text>
              <v-text-field v-model="title"
                            :rules="[v => !!v || 'Le titre est obligatoire', v => v.length <= 80 || 'Ce champ ne peut contenir que 80 caractères maximum']"
                            :label="`${$t('form.organization.title')} *`"
                            :counter="80"
              />
              <v-text-field v-model="address"
                            :label="$t('form.organization.address')"/>
              <v-text-field v-model="siret"
                            :label="$t('form.organization.siret')"/>
              <v-row>
                <v-col>
                  <v-select
                      :items="organizationTypes"
                      v-model="organizationType"
                      label="Type d'organisation *"
                      :disabled="!isCreation"
                      v-if="isRecruiter"
                      :rules="[v => !!v || `Le type d'organisation est obligatoire`]"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-checkbox v-model="privateUsers"
                              persistent-hint
                              hint="Contraint les utilisateurs associés au canal de cette organisation à ne voir que les recrutements de cette organisation"
                              label="Utilisateurs privés"
                              v-if="isRecruiter"
                  />
                </v-col>
                <v-col>
                  <v-checkbox v-model="privateJobs"
                              persistent-hint
                              hint="Contraint les postes de l'organisation à ne recevoir de candidatures que des utilisateurs du canal de cette organisation"
                              label="Postes privés"
                              v-if="isRecruiter"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-checkbox v-model="mandatoryIdentity"
                              persistent-hint
                              hint="Contraint les utilisateurs associés au canal de cette organisation à renseigner leur identité"
                              label="Identité obligatoire"
                              v-if="isRecruiter"
                  />
                </v-col>
                <v-col>
                  <v-checkbox v-model="internal"
                              persistent-hint
                              hint="Entreprise dont les recrutements sont gérés en interne, et non directement par le client."
                              label="Géré par Erhgo"
                              v-if="isRecruiter"
                  />
                </v-col>
              </v-row>
              <v-select
                :items="recruiters"
                clearable
                v-model="refererRecruiterCode"
                label="Organisation associée à cet employeur"
                persistent-hint
                v-if="isEmployer"
                :rules="[v => !!v || 'Veuillez préciser l\'organisation associée']"
              />

              <v-select
                :items="projects"
                multiple
                v-model="selectedProjectIds"
                label="Projets associés à l'organisation"
                no-data-text="Aucun projet disponible"
                v-if="isConsortiumOrOT || isSourcing"
              />
              <v-select
                :items="selectedProjects"
                v-model="selectedDefaultProjectId"
                label="Projet par défault à associer à l'organisation"
                v-if="isConsortiumOrOT && selectedProjects.length"
              />
              <v-select
                :items="consortiums"
                multiple
                v-model="selectedConsortiumIds"
                label="Groupements d'employeurs associés à l'employeur"
                no-data-text="Aucun groupement d'employeur disponible"
                v-if="isEmployer"
              />
              <div class="px-4 pa-4">
                <v-text-field v-model="forcedUrl"
                              max-length="255"
                              label="URL jenesuisPASunCV"
                              hint="Si renseignée, cette adresse web est affichée sur l'ensemble des recrutements de l'organisation ; elle n'est pas modifiable par l'utilisateur sourcing."
                              persistent-hint
                              clearable/>
              </div>

              <v-card v-if="isRecruiter" class="pa-4 mb-4">
                <v-card-title>Diffusion des offres</v-card-title>
                <v-card-text>
                  <v-checkbox v-model="diffusionCV"
                            label="Diffusion des offres sur jenesuispasuncv"
                            hint="Si coché, les offres de cette organisation seront diffusées sur jenesuispasuncv"
                            persistent-hint/>
                  <v-checkbox v-model="diffusionHandicap"
                            label="Diffusion des offres sur jenesuispasunhandicap"
                            hint="Si coché, les offres de cette organisation seront diffusées sur jenesuispasunhandicap"
                            persistent-hint/>
                </v-card-text>
              </v-card>
              <div class="pt-3 subheading"
                   style="color: rgba(0,0,0,.54);">
                {{ $t('form.organization.description') }} :
              </div>
              <p v-if="descriptionIsTooLong" class="error--text">Description trop longue (2000 caractères maximum)</p>
              <vue-editor class="mt-2"
                          v-model="description"/>
              <v-expansion-panels v-if="isRecruiter && organizationType !== OrganizationType.SOURCING">
                <v-expansion-panel>
                  <v-expansion-panel-header>
                    <h4 id="refusalEmailTemplateTitle" class="d-flex align-center">
                      <v-icon class="mr-2"
                              color="primary">
                        mail
                      </v-icon>
                      Template d'email de refus de candidature par défaut
                    </h4>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-form v-model="validForm" ref="form">
                      <template>
                        <v-text-field v-model="emailTemplate.subject" label="Objet de l'email" required
                                      :counter="100"
                                      :rules="[rules.required, v => v.length <= 100 || 'Texte trop long']"/>
                        <v-text-field v-model="emailTemplate.emailFrom" label="Email de l'émetteur" required
                                      type="email"
                                      :counter="40"
                                      :rules="[rules.required, v => v.length <= 64 || 'Texte trop long']"
                                      :suffix="defaultEmailDomain"/>
                        <v-text-field v-model="emailTemplate.authorAlias" label="Alias de l'émetteur" :counter="40"
                                      :rules="[v => v.length <= 40 || 'Texte trop long']"/>
                        <v-textarea v-model="emailTemplate.content"
                                    :counter="10000"
                                    :rules="[rules.required, v => v.length <= 10000 || 'Texte trop long']"
                                    label="Contenu de l'email"
                                    required/>
                      </template>
                    </v-form>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
              <v-alert type="error" outlined v-if="showError">
                Une erreur est survenue à l'enregistrement des données, veuillez réessayer ou contacter le support.
              </v-alert>
            </v-card-text>
            <v-card-actions>
              <v-btn color="grey lighten-5"
                     large
                     :to="backRoute">
                <v-icon small
                        left>undo
                </v-icon>
                {{ $t('action.return') }}
              </v-btn>
              <v-spacer/>
              <v-btn width="50%"
                     large
                     :color="submitColor"
                     @click="submit"
                     :loading="loading">
                {{$t(`action.${isCreation?'create':'update'}`)}} &nbsp;
                <v-icon large
                        right>{{submitIcon}}
               </v-icon>
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { OrganizationType } from 'erhgo-api-client';

export default {
  props: {
    organizationCode: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      id: '',
      code: '',
      title: '',
      description: '',
      address: '',
      siret: '',
      forcedUrl: '',
      privateUsers: false,
      privateJobs: false,
      mandatoryIdentity: false,
      internal: false,
      diffusionCV: true,
      diffusionHandicap: true,
      organizationType: null,
      loading: false,
      submitColor: 'primary',
      submitIcon: 'save',
      organizationTypes: [
        {text: 'Entreprise', value: OrganizationType.ENTERPRISE},
        {text: 'Organisation territoriale', value: OrganizationType.TERRITORIAL},
        {text: 'Groupement d\'entreprises', value: OrganizationType.CONSORTIUM},
        {text: 'Projet', value: OrganizationType.PROJECT},
        {text: 'Sourcing', value: OrganizationType.SOURCING},
        {text: 'Employer', value: OrganizationType.EMPLOYER},
      ],
      OrganizationType,
      projects: [],
      consortiums: [],
      selectedProjectIds: [],
      selectedDefaultProjectId: null,
      selectedConsortiumIds: [],
      showError: false,
      refererRecruiterCode: null,
      recruiters: [],
      defaultEmailDomain: '@jenesuispasuncv.fr',
      emailTemplate: {
        subject: '',
        emailFrom: 'recrutement',
        authorAlias: 'Iris',
        content: '',
      },
      validForm: false,
      rules: {
        required: v => !!v || 'Ce champ est obligatoire',
        email: v => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v) || `L'email doit être valide`,
      },
    };
  },
  async created() {
    if (!this.isCreation) await this.getOrganization(this.$route.params.id);
    else this.refererRecruiterCode = this.organizationCode;

    await this.refreshRecruiters();
  },
  computed: {
    descriptionIsTooLong() {
      return this.description && this.description.length > 2000;
    },
    isCreation() {
      return !this.$route.params.id;
    },
    isEmployer() {
      return this.organizationCode;
    },
    isRecruiter() {
      return !this.isEmployer;
    },
    backRoute() {
      const {organizationCode} = this;
      return organizationCode ? {name: 'employer_repository', params: {organizationCode}}
        : {name: 'organizations_repository'};
    },
    isConsortiumOrOT() {
      return this.organizationType === OrganizationType.TERRITORIAL || this.organizationType === OrganizationType.CONSORTIUM;
    },
    isSourcing() {
      return this.organizationType === OrganizationType.SOURCING;
    },
    selectedProjects() {
      return this.projects.filter(p => this.selectedProjectIds.includes(p.value));
    },
  },
  watch: {
    selectedProjectIds(ids) {
      if (!ids.includes(this.selectedDefaultProjectId)) {
        this.selectedDefaultProjectId = null;
      }
    },
  },
  methods: {
    async refreshRecruiters() {
      const allRecruiters = (await this.$api.getAllRecruiters()).data;
      this.projects = allRecruiters
        .filter(e => e.organizationType === OrganizationType.PROJECT)
        .map(e => ({text: e.title, value: e.id}));
      // FIXME: should restrict to consortium having access to project and visible only if recruiter is a project ?
      this.consortiums = allRecruiters
        .filter(e => e.organizationType === OrganizationType.CONSORTIUM)
        .map(e => ({text: e.title, value: e.id}));
      this.recruiters = allRecruiters
        .filter(e => e.organizationType !== OrganizationType.SOURCING)
        .map(e => ({text: e.title, value: e.code}));
    },
    async getOrganization(id) {
      this.loading = true;

      try {
        const data = (await this.$api.getOrganization(id)).data;
        this.id = data.id;
        this.code = data.code;
        this.title = data.title;
        this.description = data.description;
        this.address = data.address;
        this.organizationType = data.organizationType;
        this.selectedProjectIds = (data.projects || []).map(p => p.id);
        this.selectedDefaultProjectId = data.defaultProject?.id;
        this.refererRecruiterCode = data.refererRecruiter?.code;
        this.selectedConsortiumIds = (data.consortiums || []).map(p => p.id);
        this.privateUsers = data.privateUsers;
        this.privateJobs = data.privateJobs;
        this.mandatoryIdentity = data.mandatoryIdentity;
        this.internal = data.internal;
        this.forcedUrl = data.forcedUrl;
        this.siret = data.siret;

        if (data.defaultDiffusionType) {
          this.diffusionCV = data.defaultDiffusionType === 'CV' || data.defaultDiffusionType === 'BOTH';
          this.diffusionHandicap = data.defaultDiffusionType === 'HANDICAP' || data.defaultDiffusionType === 'BOTH';
        }
        if (data.refusalEmailTemplate !== null) {
          this.emailTemplate = {
            ...data.refusalEmailTemplate,
            emailFrom: data.refusalEmailTemplate.emailFrom.split('@')[0],
          };
        }
      } catch (e) {
        this.showError = true;
      } finally {
        this.loading = false;
      }
    },
    async submit() {
      this.loading = true;
      this.showError = false;
      try {
        if (this.$refs.form.validate() && !this.descriptionIsTooLong) {
          const refusalEmailTemplate = this.isRecruiter ? {
            ...this.emailTemplate,
            emailFrom: `${this.emailTemplate.emailFrom}${this.defaultEmailDomain}`,
          } : null;

          const command = {
            id: this.id,
            title: this.title,
            description: this.description,
            address: this.address,
            organizationType: this.isEmployer ? OrganizationType.EMPLOYER : this.organizationType,
            projectIds: this.selectedProjectIds,
            defaultProjectId: this.selectedDefaultProjectId,
            consortiumIds: this.selectedConsortiumIds,
            refererRecruiterCode: this.refererRecruiterCode,
            privateUsers: this.privateUsers,
            privateJobs: this.privateJobs,
            mandatoryIdentity: this.mandatoryIdentity,
            internal: this.internal,
            forcedUrl: this.forcedUrl,
            siret: this.siret,
            refusalEmailTemplate,
            defaultDiffusionType: this.getDiffusionType(),
          };
          const response = await this.$api.saveOrganization(command);
          this.submitColor = 'success';
          this.submitIcon = 'mdi-check-outline';
          this.$root.$emit('updtOrganization', response.data);
          setTimeout(() => this.clear(), 2000);
        }
      } catch (e) {
        this.showError = true;
        throw e;
      } finally {
        this.loading = false;
      }
    },
    getDiffusionType() {
      if (this.diffusionCV && this.diffusionHandicap) {
        return 'BOTH';
      } else if (this.diffusionCV) {
        return 'CV';
      } else if (this.diffusionHandicap) {
        return 'HANDICAP';
      }
      return 'NONE';
    },

    async clear() {
      await this.refreshRecruiters();
      if (this.isCreation) {
        this.title = '';
        this.address = '';
        this.description = '';
        this.forcedUrl = '';
        this.organizationType = null;
        this.$refs.form && this.$refs.form.resetValidation();
        this.selectedProjects = [];
        this.selectedDefaultProjectId = null;
      }

      this.submitColor = 'primary';
      this.submitIcon = 'save';
    },
  },
};
</script>

<style>
</style>
