<template>
  <div>
    <v-menu
      v-model="menuOpen"
      :close-on-content-click="false"
      offset-y
      transition="scale-transition"
    >
      <template v-slot:activator="{ on, attrs }">
        <div class="d-flex align-center">
          <v-btn
            v-bind="attrs"
            v-on="on"
            outlined
            class="diffusion-btn"
          >
            <v-icon left>mdi-share-variant</v-icon>
            {{ buttonLabel }}
            <v-spacer />
            <v-icon right>mdi-menu-down</v-icon>
          </v-btn>
          <v-btn
            v-if="currentDiffusionType !== null"
            icon
            small
            class="ml-2"
            title="Réinitialiser à la valeur par défaut"
            @click.stop="nullifyDiffusionType"
          >
            <v-icon>mdi-cancel</v-icon>
          </v-btn>
        </div>
      </template>

      <v-card min-width="300">
        <v-card-title class="text-subtitle-1">Diffusion de l'offre</v-card-title>
        <v-card-text>
          <v-checkbox v-model="diffusionCV"
                      label="Diffuser sur jenesuisPASunCV"
                      hint="Si coché, cette offre sera diffusée sur jenesuispasuncv"
                      dense
                      hide-details="auto"
                      @change="updateDiffusionType" />
          <v-checkbox v-model="diffusionHandicap"
                      label="Diffuser sur jenesuisPASunHANDICAP"
                      hint="Si coché, cette offre sera diffusée sur jenesuispasunhandicap"
                      dense
                      hide-details="auto"
                      @change="updateDiffusionType" />
        </v-card-text>
        <v-divider v-if="showSaveButton"/>
        <v-card-actions v-if="showSaveButton">
          <v-spacer />
          <v-btn text @click="menuOpen = false">Annuler</v-btn>
          <v-btn color="primary" @click="saveDiffusionType" :loading="loading">
            Enregistrer
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-menu>

    <v-snackbar v-model="showSnackbar" :color="snackbarColor" :timeout="3000">
      {{ snackbarText }}
    </v-snackbar>
  </div>
</template>

<script>
import { DiffusionType } from 'erhgo-api-client';

export default {
  name: 'DiffusionTypeEditor',
  props: {
    recruitmentId: {
      type: Number,
      required: true,
    },
    initialDiffusionType: {
      type: String,
      default: null,
    },
    defaultDiffusionType: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      diffusionCV: false,
      diffusionHandicap: false,
      loading: false,
      showSaveButton: false,
      showSnackbar: false,
      snackbarText: '',
      snackbarColor: 'success',
      currentDiffusionType: null,
      menuOpen: false,
    };
  },
  computed: {
    buttonLabel() {
      const diffusionLabels = {
        'BOTH': 'Diffusion sur les deux sites',
        'CV': 'Diffusion sur jenesuisPASunCV',
        'HANDICAP': 'Diffusion sur jenesuisPASunHANDICAP',
        'NONE': 'Aucune diffusion',
      };

      return this.currentDiffusionType ? diffusionLabels[this.currentDiffusionType] : `Diffusion par défaut ${diffusionLabels[this.defaultDiffusionType]}`;
    },
  },
  watch: {
    initialDiffusionType: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.setCheckboxesFromDiffusionType(newValue);
        }
        this.currentDiffusionType = newValue;
      },
    },
  },
  methods: {
    setCheckboxesFromDiffusionType(diffusionType) {
      this.diffusionCV = diffusionType === 'CV' || diffusionType === 'BOTH';
      this.diffusionHandicap = diffusionType === 'HANDICAP' || diffusionType === 'BOTH';
    },
    getDiffusionTypeFromCheckboxes() {
      if (this.diffusionCV && this.diffusionHandicap) {
        return DiffusionType.BOTH;
      } else if (this.diffusionCV) {
        return DiffusionType.CV;
      } else if (this.diffusionHandicap) {
        return DiffusionType.HANDICAP;
      } else {
        return DiffusionType.NONE;
      }
    },
    updateDiffusionType() {
      const newDiffusionType = this.getDiffusionTypeFromCheckboxes();
      this.showSaveButton = this.currentDiffusionType !== newDiffusionType;
    },
    async saveDiffusionType() {
      this.loading = true;
      try {
        const diffusionType = this.getDiffusionTypeFromCheckboxes();

        await this.$api.updateRecruitmentDiffusionType(this.recruitmentId, {
          diffusionType: diffusionType,
        });

        this.currentDiffusionType = diffusionType;
        this.showSaveButton = false;
        this.menuOpen = false;
        this.snackbarText = 'Paramètres de diffusion mis à jour avec succès';
        this.snackbarColor = 'success';
        this.showSnackbar = true;
        this.$emit('updated', diffusionType);
      } catch (error) {
        this.snackbarText = 'Erreur lors de la mise à jour des paramètres de diffusion';
        this.snackbarColor = 'error';
        this.showSnackbar = true;
      } finally {
        this.loading = false;
      }
    },
    async nullifyDiffusionType() {
      this.loading = true;
      try {
        // Call API to set diffusionType to null
        await this.$api.updateRecruitmentDiffusionType(this.recruitmentId, {
          diffusionType: null,
        });

        // Update local state
        this.currentDiffusionType = null;
        this.setCheckboxesFromDiffusionType(this.defaultDiffusionType);
        this.showSaveButton = false;
        this.menuOpen = false;
        this.snackbarText = 'Paramètres de diffusion réinitialisés avec succès';
        this.snackbarColor = 'success';
        this.showSnackbar = true;
        this.$emit('updated', null);
      } catch (error) {
        this.snackbarText = 'Erreur lors de la réinitialisation des paramètres de diffusion';
        this.snackbarColor = 'error';
        this.showSnackbar = true;
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped>
.diffusion-btn {
  min-width: 220px;
  text-align: left;
  justify-content: flex-start;
}
</style>
