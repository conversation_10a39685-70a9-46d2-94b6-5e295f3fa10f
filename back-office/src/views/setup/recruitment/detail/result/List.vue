<template>
  <v-container fluid>
    <v-row v-if="recruitment">
      <v-col cols="6">
        <v-breadcrumbs class="breadcrumb" :items="breadcrumbsItems">
          <template v-slot:divider>
            <v-icon slot="divider">chevron_right</v-icon>
          </template>
          <template v-slot:item="{ item }">
            <v-breadcrumbs-item
              exact
              :to="item.to"
              :disabled="item.disabled">
              {{ item.text }}
            </v-breadcrumbs-item>
          </template>
        </v-breadcrumbs>
      </v-col>
      <v-spacer/>
      <v-col cols="2" class="text-right">
        <downloader-btn
          outlined
          class="ml-3"
          small
          :download="exportCandidatures"
          :title="`candidatures-${recruitment.id}.csv`"
          extension="csv"
        >
          <v-icon>archive</v-icon>
          Exporter
        </downloader-btn>
      </v-col>
      <v-col cols="2" v-if="recruitment.state === 'SELECTION'">
        <card-btn
          :to="{ name: 'terminate_recruitment', params: { organization_code: $route.params.organization_code, recruitmentCode: recruitment.code } }">
          <h4>Terminer ce recrutement</h4>
        </card-btn>
      </v-col>
      <v-col cols="2" v-if="recruitment.state === 'PUBLISHED'">
        <card-btn :recruitment-code="recruitment.code"
                  :to="{ name: 'close_recruitment', params: { organization_code: $route.params.organization_code, recruitmentCode: recruitment.code } }">
          <h4>Fermer ce recrutement</h4>
        </card-btn>
      </v-col>
      <v-col cols="12" class="d-flex">
        <v-col cols="8" />
        <v-col cols="4" class="text-right">
          <diffusion-type-editor
            :recruitment-id="recruitment.id"
            :initial-diffusion-type="recruitment.diffusionType"
            :default-diffusion-type="recruitment.defaultDiffusionType"
          />
        </v-col>
      </v-col>
      <v-col cols="12">
        <candidatures-result
          :recruitment="recruitment"
          allows-confirm-candidature
          id="candidateResultUnfinished"
          @confirmed="refreshIndex++"
        />
        <candidatures-result
          matching
          :recruitment="recruitment"
          id="candidateResultMatching"
          :key="`matching-${refreshIndex}`"
        />
        <candidatures-result
          :matching="false"
          :recruitment="recruitment"
          id="candidateResultNotMatching"
          :key="`not-matching-${refreshIndex}`"
        />
      </v-col>
    </v-row>
    <v-progress-circular indeterminate v-else/>

  </v-container>
</template>

<script>
import CardBtn from 'odas-plugins/CardBtn';
import CandidaturesResult from './Result';
import DownloaderBtn from 'odas-plugins/DownloaderBtn.vue';
import DiffusionTypeEditor from './DiffusionTypeEditor.vue';

export default {
  components: {
    DownloaderBtn,
    CardBtn,
    CandidaturesResult,
    DiffusionTypeEditor,
  },
  name: 'List',
  data() {
    return {
      recruitment: null,
      refreshIndex: 1,
    };
  },
  async created() {
    this.recruitment = (await this.$api.getRecruitmentForMatching(this.recruitment_id)).data;
  },
  props: {
    recruitment_id: {
      type: Number,
      required: true,
    },
  },
  computed: {
    title() {
      return `${this.recruitment?.jobTitle} - ${this.recruitment?.profileTitle}`;
    },
    breadcrumbsItems: {
      get() {
        return [
          {
            text: 'Recrutements',

            to: {
              name: 'recruitments_list',
              params: {organization_code: this.$route.params.organization_code},
            },
          },
          {
            text: this.title,
            disabled: true,
          },
        ];
      },
    },
  },
  methods: {
    async exportCandidatures() {
      return (await this.$api.exportCandidatures(this.recruitment.code, undefined, { responseType: 'blob' })).data;
    },
  },
};
</script>
