<template>
  <v-container fluid class="py-0">
    <v-dialog :value="true" v-if="!!confirmAction" max-width="600">
      <v-card>
        <v-card-title class="text-h5">
          {{ confirmAction.title }}
        </v-card-title>
        <v-card-text>
          {{ confirmAction.text }}
        </v-card-text>
        <v-card-actions>
          <v-btn
            color="primary"
            outlined
            @click="confirmAction = null"
          >
            Non, annuler
          </v-btn>
          <v-spacer/>
          <v-btn
            color="primary"
            @click="doConfirm(confirmAction.action, confirmAction.code, confirmAction.organization_code)"
          >
            Oui
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-row>
      <v-col>
        <h1 class="d-flex align-center">
          <v-row>
            <v-col cols="auto">
              <v-icon large
                      class="mr-2"
                      color="primary">mdi-receipt
              </v-icon>
              Suivi des recrutements
            </v-col>
          </v-row>
        </h1>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col class="text-center">
        <v-btn color="primary" @click="onGoToCreateRecruitmentPageClick" :loading="goToCreateRecruitmentPageOngoing">
          Créer un recrutement
        </v-btn>
        <downloader-btn
          outlined
          class="ml-3"
          small
          :download="exportCandidatures"
          :title="`candidatures-spontanées-${$route.params.organization_code}.csv`"
          extension="csv"
        >
          <v-icon>archive</v-icon>
          Exporter les candidatures spontanées
        </downloader-btn>
        <v-btn
          outlined
          color="secondary"
          class="ml-3"
          small
          @click="reindexRecruitments"
          :loading="reindexing"
        >
          <v-icon>mdi-refresh</v-icon>
          Réindexer les offres
        </v-btn>
      </v-col>
    </v-row>
    <offers-importer :service=service />
    <v-row no-gutters>
      <v-col cols="12" md="6" class="pr-md-10">
        <v-row no-gutters>
          <v-col cols="12">
            <v-text-field v-model="service.query"
                          label="Rechercher un recrutement"
                          hint="Recherche dans le titre du poste, le titre du profil, l'employeur et la localisation"
                          persistent-hint/>
          </v-col>
          <v-col cols="12">
            <v-autocomplete
              class="mt-7"
              hint="Choisissez des organisations pour filtrer les recrutements"
              persistent-hint
              multiple
              :menu-props="{ offsetY: true }"
              :items="organizationAndProjects"
              v-model="service.selectedOrganizationCodes"
              label="Filtrer par organisation"
              :item-text="o => `${o.title}${o.organizationType === 'SOURCING' ? ' (sourcing)' : ''}`"
              item-value="code"
              @input="searchInput=null"
              :search-input.sync="searchInput"
              clearable
              flat/>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="6">
        <v-row no-gutters>
          <v-col cols="12">
            <v-checkbox label="Uniquement les recrutements avec de nouvelles candidatures"
                        v-model="service.withNewCandidaturesOnly"/>
          </v-col>
          <v-col cols="12">
            <v-checkbox label="Uniquement les recrutements ouverts"
                        v-model="service.withOpenRecruitmentOnly"/>
          </v-col>
          <v-radio-group
            v-if="!organizationCode"
            v-model="filterRecruitmentsBy"
          >
            <v-radio
              label="Tous les recrutements"
              :value="filters.ALL"
            />
            <v-radio
              label="Uniquement les recrutements gérés par Erhgo"
              :value="filters.INTERNAL"
            />
            <v-radio
              label="Uniquement les recrutements sourcing"
              :value="filters.SOURCING"
            />
          </v-radio-group>
        </v-row>
      </v-col>

    </v-row>
    <v-alert v-if="service.error" :value="service.error">
      Une erreur est survenue lors du chargement des recrutements. Merci de ré-essayer ou de contacter le support.
    </v-alert>
    <v-row v-else>
      <v-col cols="12">
        <v-alert :color="copyHrefLinkStatus.success ? 'success' : 'error'"
                 icon="info" :value="true" outlined v-if="!!copyHrefLinkStatus">
          {{ copyHrefLinkStatus.message }}
        </v-alert>
        <v-data-table :headers="headers.filter(h => !h.forSourcing || includesSourcing)"
                      :items="content"
                      :sort-by.sync="service.pagination.by"
                      :page.sync="service.pagination.page"
                      :sort-desc.sync="service.pagination.descending"
                      :items-per-page.sync="service.pagination.rowsPerPage"
                      :footer-props="{
                              'items-per-page-options': rowsPerPage,
                              'items-per-page-text': 'Lignes par page',
                            }"
                      :loading="service.loading"
                      class="elevation-15"
                      item-key="id"
                      :server-items-length="service.page?service.page.totalNumberOfElements:null"
                      no-data-text="Aucun recrutement"
        >
          <template v-slot:item="props">
            <tr>
              <td class="text-left">{{ props.item.publicationDate | formatDate }}</td>
              <td class="text-left">{{
                  props.item.lastProcessingDate ? (moment(props.item.lastProcessingDate).format('DD/MM/YY')) : 'Aucune'
                }}
              </td>
              <td class="text-left">
                {{ props.item.lastProcessingType ? $t(`processingType.${props.item.lastProcessingType}`) : 'Aucun' }}
              </td>
              <td class="text-left job-result">{{ props.item.jobTitle }}</td>
              <td class="text-left" :class="{'text-italic': !props.item.employerTitle}">
                {{ props.item.employerTitle || '(Non renseigné)' }}
              </td>
              <td class="text-left">{{ props.item.recruiterTitle }}</td>
              <td class="text-left" v-if="includesSourcing">{{ props.item.sourcingHost }}</td>
              <td class="text-left">{{ props.item.city }}</td>
              <td class="text-left">{{ $t(`recruitmentState.${props.item.state}`) }}</td>
              <td class="text-center">{{
                  (props.item.candidaturesCount.totalCandidatureCount.fromUser + props.item.candidaturesCount.totalCandidatureCount.generated) || 'Aucune'
                }}
              </td>
              <td class="text-center">
                {{
                  `${props.item.candidaturesCount.totalCandidatureCount.fromUser} (${
                    props.item.candidaturesCount.newCandidatureCount.fromUser
                  })`
                }}
              </td>
              <td class="text-center">
                {{
                  `${props.item.candidaturesCount.totalCandidatureCount.generated} (${
                    props.item.candidaturesCount.newCandidatureCount.generated
                  })`
                }}
              </td>
              <td class="text-center">{{
                  ((props.item.candidaturesCount.newCandidatureCount.fromUser + props.item.candidaturesCount.newCandidatureCount.generated) + props.item.candidaturesCount.toContactCandidatureCount + props.item.candidaturesCount.contactedCandidatureCount) || 'Aucune'
                }}
              </td>
              <td class="text-center">
                {{ props.item.candidaturesCount.refusedCandidatureCount || 'Aucune' }}
              </td>
              <td class="text-center" v-if="includesSourcing">
                {{
                  props.item.sendNotificationState ? $t(`recruitmentEmailState.${props.item.sendNotificationState}`) : `Pas d'envoi d'email`
                }}
                {{ props.item.sendNotificationDate ? " le " : "" }}
                {{ props.item.sendNotificationDate | formatDate }}
              </td>
              <td class="text-center" v-if="includesSourcing">
                {{ props.item.mailNotificationCount }} / {{ props.item.mobileNotificationsCount }}
              </td>
              <td :key="`result-${service.refreshId}-${props.item.id}`">
                <v-tooltip top>
                  Copier le lien de candidature à diffuser
                  <template v-slot:activator="{ on }">
                    <v-btn icon v-on="on"
                           v-if="props.item.state === 'PUBLISHED'"
                           @click="copy(props.item.code)">
                      <v-icon>content_paste</v-icon>
                    </v-btn>
                  </template>
                </v-tooltip>
                <v-tooltip top>
                  Voir les candidatures
                  <template v-slot:activator="{ on }">
                    <v-btn icon :to="{
                    name: 'recruitment_result',
                    params: {
                        recruitment_id: props.item.id,
                        organization_code: organizationCode || props.item.recruiterCode
                    }
                    }" v-on="on">
                      <v-icon>visibility</v-icon>
                    </v-btn>
                  </template>
                </v-tooltip>
                <v-tooltip top>
                  Modifier le recrutement
                  <template v-slot:activator="{ on }">
                    <v-btn icon
                           :to="{
                       name: 'recruitment_edit',
                       params: {organization_code:  organizationCode || props.item.recruiterCode, recruitmentCode:props.item.code}
                    }" v-on="on">
                      <v-icon>edit</v-icon>
                    </v-btn>
                  </template>
                </v-tooltip>
                <v-tooltip top>
                  Fermer le recrutement
                  <template v-slot:activator="{ on }">
                    <v-btn icon
                           @click="confirmAction = {...actionClose, code: props.item.code, organization_code :organizationCode || props.item.recruiterCode }"
                           v-if="props.item.state === RecruitmentState.PUBLISHED"
                           v-on="on">
                      <v-icon>close</v-icon>
                    </v-btn>
                  </template>
                </v-tooltip>
                <v-tooltip top>
                  Terminer le recrutement
                  <template v-slot:activator="{ on }">
                    <v-btn icon
                           @click="confirmAction = {...actionTerminate, code: props.item.code, organization_code : organizationCode || props.item.recruiterCode}"
                           v-if="props.item.state === RecruitmentState.SELECTION" v-on="on">
                      <v-icon>close</v-icon>
                    </v-btn>
                  </template>
                </v-tooltip>
              </td>
            </tr>
          </template>
          <template v-slot:footer.page-text="props">
            Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

import { OrganizationType, RecruitmentSort, RecruitmentState } from 'erhgo-api-client';
import { RecruitmentListService } from './RecruitmentListService';
import { baseUrl, logErrorToServer } from 'odas-plugins';
import Vue from 'vue';
import moment from 'moment/moment';
import OffersImporter from '@/views/setup/recruitment/list/OffersImporter.vue';
import DownloaderBtn from 'odas-plugins/DownloaderBtn.vue';


const filters = {
  ALL: 0,
  INTERNAL: 1,
  SOURCING: 2,
};

export default {
  name: 'RecruitmentListView',
  components: {OffersImporter, DownloaderBtn},
  props: {
    organizationCode: String,
  },
  data() {
    return {
      moment,
      searchInput: null,
      confirmAction: null,
      copyHrefLinkStatus: null,
      service: null,
      rowsPerPage: [10, 25, 50, 100, 200],
      reindexing: false,
      RecruitmentState,
      actionClose: {
        title: 'Fermer le recrutement',
        text: 'En le fermant, vous ne pourrez plus positionner de candidat sur ce recrutement.',
        action: 'close_recruitment',
      },
      actionTerminate: {
        title: 'Terminer le recrutement',
        text: 'En le terminant, vous ne pourrez plus accéder aux candidats sur ce recrutement.',
        action: 'terminate_recruitment',
      },
      headers: [
        {
          text: 'Date de publication',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.PUBLICATION_DATE,
        },
        {
          text: 'Date de dernière activité',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.LAST_PROCESSING_DATE,
        },
        {
          text: 'Type de dernière activité',
          align: 'left',
          sortable: false,
        },
        {
          text: 'Titre du poste',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.JOB,
        },
        {
          text: 'Employeur',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.EMPLOYER,
        },
        {
          text: 'Organisation',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.RECRUITER,
        },
        {
          text: 'Organisation Invitante',
          align: 'left',
          sortable: false,
          value: 'sourcingHost',
          forSourcing: true,
        },
        {
          text: ' Ville ',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.CITY,
        },
        {
          text: 'État',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.STATE,
        },
        {
          text: 'Candidatures totales',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.CANDIDATURE_COUNT,
        },
        {
          text: 'Candidatures sur offre (dont nouvelles)',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.CANDIDATURE_COUNT,
        },
        {
          text: 'Candidatures proposées par jenesuispasuncv (dont nouvelles)',
          align: 'left',
        },
        {
          text: 'Non traitées',
          align: 'left',
        },
        {
          text: 'Nb candidatures refusées',
          align: 'left',
        },
        {
          text: 'État envoi d\'email',
          align: 'left',
          sortable: true,
          value: RecruitmentSort.SEND_MAIL_DATE,
          forSourcing: true,
        },
        {
          text: 'Nb mails / notifs',
          align: 'left',
          sortable: false,
          value: 'mailNotificationCount',
          forSourcing: true,
        },
        {
          text: '',
          align: 'left',
          sortable: false,
          width: '200px',
        },
      ],
      organizationAndProjects: [],
      filterRecruitmentsBy: null,
      filters,
      goToCreateRecruitmentPageOngoing: false,
    };
  },
  async created() {
    const withOpenOnly = this.$route.query.openOnly !== 'false';
    const withNewOnly = this.$route.query.newOnly && this.$route.query.newOnly !== 'false';
    const organizationTypeFilter = this.$route.query.organizationTypeFilter;
    const internalOnly = !this.organizationCode && this.$route.query.internal === 'true';

    this.service = new RecruitmentListService(this.organizationCode, this.selectedOrganizationCodes, withNewOnly, withOpenOnly, internalOnly, organizationTypeFilter);
    if (internalOnly) {
      this.filterRecruitmentsBy = this.filters.INTERNAL;
    } else if (organizationTypeFilter === this.filters.SOURCING) {
      this.filterRecruitmentsBy = this.filters.SOURCING;
    } else {
      this.filterRecruitmentsBy = this.filters.ALL;
    }
    if (!!this.organizationCode) {
      this.organizationAndProjects = (await this.$api.getAllRecruitersForOrganization(this.$route.params.organization_code)).data;
      const organization = this.organizationAndProjects.filter(o => o.code === this.$route.params.organization_code)[0];
      const defaultProject = this.organizationAndProjects.filter(o => o.code === organization.defaultProject?.code)[0];
      this.service.selectedOrganizationCodes = !!defaultProject?.code ? [defaultProject.code] : [];
    } else {
      this.organizationAndProjects = (await this.$api.getAllRecruiters()).data;
      this.service.debouncedRefresh.fn();
    }
  },
  computed: {
    content() {
      return this.service?.page?.content || [];
    },
    pagination() {
      return this.service?.pagination;
    },
    includesSourcing() {
      return !this.organizationCode || !this.organizationAndProjects.length || !this.organizationAndProjects.every(o => o.organizationType !== OrganizationType.SOURCING);
    },
  },
  watch: {
    pagination: {
      handler(newVal, oldVal) {
        if (oldVal && newVal) {
          this.service.debouncedRefresh.fn();
        }
      },
      deep: true,
    },
    searchInput(newVal, oldVal) {
      if (newVal && oldVal !== newVal) {
        this.filterRecruitmentsBy = this.filters.ALL;
      }
    },
    filterRecruitmentsBy(newVal) {
      switch (newVal) {
        case this.filters.ALL:
          this.service.internal = false;
          this.service.organizationTypeFilter = null;
          break;
        case this.filters.INTERNAL:
          this.service.internal = true;
          this.service.organizationTypeFilter = null;
          break;
        case this.filters.SOURCING:
          this.service.internal = false;
          this.service.organizationTypeFilter = 'SOURCING';
          break;
      }
    },
  },
  methods: {
    async onGoToCreateRecruitmentPageClick() {
      try {
        this.goToCreateRecruitmentPageOngoing = true;
        await this.service.goToCreateRecruitmentPage();
      } finally {
        this.goToCreateRecruitmentPageOngoing = false;
      }
    },
    async exportCandidatures() {
      return (await this.$api.exportCandidatures(undefined, this.$route.params.organization_code, { responseType: 'blob' })).data;
    },
    doConfirm(action, recruitmentCode, organization_code) {
      this.$router.push({
        name: action,
        params: {
          organization_code, recruitmentCode,
        },
      });
    },
    generatePublicUrl(recruitmentCode) {
      if (!recruitmentCode) {
        logErrorToServer('Generated job page url is empty', Vue.$api);
      }
      return new URL(`${baseUrl('fo')}/jobs/${recruitmentCode}`);
    },
    copy(code) {
      try {
        navigator.clipboard.writeText(this.generatePublicUrl(code));
        this.copyHrefLinkStatus = {
          success: true,
          message: 'L\'url a été copiée dans le presse papier',
        };
        this.eraseCopyHrefLinkStatus();
      } catch (e) {
        this.onCopyError();
      }
    },
    onCopyError() {
      this.copyHrefLinkStatus = {
        success: false,
        message: 'L\'url n\'a pas été copiée dans le presse papier, veuillez réessayer.',
      };
    },
    eraseCopyHrefLinkStatus() {
      setTimeout(() => {
        this.copyHrefLinkStatus = null;
      }, 5000);
    },
    async reindexRecruitments() {
      if (!this.organizationCode) {
        return;
      }

      try {
        this.reindexing = true;
        await this.$api.reindexRecruiterRecruitments(this.organizationCode);
      } catch (error) {
        this.$api.logError(error);
      } finally {
        this.reindexing = false;
      }
    },
  },
};
</script>
