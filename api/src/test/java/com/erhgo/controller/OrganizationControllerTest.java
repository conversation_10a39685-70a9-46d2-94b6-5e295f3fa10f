package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.AbstractEntity;
import com.erhgo.domain.enums.DiffusionType;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Employer;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.generators.TestFixtures;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.openapi.dto.CustomEmailTemplateDTO;
import com.erhgo.openapi.dto.DiffusionTypeDTO;
import com.erhgo.openapi.dto.OrganizationTypeDTO;
import com.erhgo.openapi.dto.SaveOrganizationCommandDTO;
import com.erhgo.repositories.EmployerRepository;
import com.erhgo.repositories.LandingPageRepository;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dtobuilder.CustomEmailTemplateDTOBuilder;
import com.erhgo.services.search.recruitment.RecruitmentIndexer;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class OrganizationControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private RecruiterRepository recruiterRepository;

    @Autowired
    private EmployerRepository employerRepository;

    @Autowired
    private LandingPageRepository landingPageRepository;

    @SpyBean
    private KeycloakMockService keycloakService;

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @SpyBean
    private RecruitmentIndexer recruitmentIndexer;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private CustomEmailTemplateDTOBuilder customEmailTemplateDTOBuilder;

    @Autowired
    private ApplicationContext applicationContext;

    private final static String DEFAULT_GDPR_MENTION = "<p>Pour toute question relative au traitement de vos données personnelles par jenesuisPASunCV et à l’exercice de vos droits sur vos données personnelles</p><ul><li><p>Par email : <a href=\"mailto:<EMAIL>\" title=\"mailto:<EMAIL>\"><u><EMAIL></u></a></p></li><li><p>Par courrier postal adressé à : LE TRAVAIL REEL, Monsieur le Délégué à la protection des données – 40 cours du Docteur Long 69003 Lyon</p></li></ul>";

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void external_consultant_should_read_organization() throws Exception {
        mvc.perform(get(realUrl("/organization/for-code/%s".formatted(TestFixtures.E_02_SOGILIS_CODE)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void getEmployers() throws Exception {
        var recruiter = organizationGenerator.createRecruiter("R-0015", AbstractOrganization.OrganizationType.TERRITORIAL);
        var project = organizationGenerator.createRecruiter("P-0016", AbstractOrganization.OrganizationType.PROJECT);
        var other = organizationGenerator.createRecruiter("P-0017", AbstractOrganization.OrganizationType.PROJECT);

        organizationGenerator.createEmployer("M-01", recruiter);
        organizationGenerator.createEmployer("M-02", recruiter);
        organizationGenerator.createEmployer("X-03", recruiter);
        organizationGenerator.createEmployer("M-04", project);
        organizationGenerator.createEmployer("M-05", other);

        set_OT_projects(recruiter, List.of(project, other), project);

        mvc.perform(get(realUrl("/organization/list"))
                        .param("refererRecruiters", recruiter.getCode() + "," + project.getCode())
                        .param("filter", "m")
                        .param("size", "10")
                        .param("page", "0")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(TestUtils.jsonMatchesContent("recruiters"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void getOrga_sort_projects() throws Exception {
        var recruiter = organizationGenerator.createRecruiter("T-0055", AbstractOrganization.OrganizationType.TERRITORIAL);
        var project1 = organizationGenerator.createRecruiter("P-5516", "P", AbstractOrganization.OrganizationType.PROJECT);
        var project2 = organizationGenerator.createRecruiter("P-5517", "a", AbstractOrganization.OrganizationType.PROJECT);
        var project3 = organizationGenerator.createRecruiter("P-5518", "z", AbstractOrganization.OrganizationType.PROJECT);


        set_OT_projects(recruiter, List.of(project1, project2, project3), project2);

        performGetAndExpect("/organization/for-id/%d".formatted(recruiter.getId()), "organizationDetail", true);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void getAllRecruiters() {
        organizationGenerator.createRecruiter("R-0015", "z", AbstractOrganization.OrganizationType.TERRITORIAL);
        organizationGenerator.createRecruiter("R-007", "a", AbstractOrganization.OrganizationType.TERRITORIAL);
        organizationGenerator.createRecruiter("S-3232", "a2", AbstractOrganization.OrganizationType.SOURCING);
        applicationContext.getBean(RecruiterMotherObject.class).withCode("S-424242").withExternalUrl("http://pi.po").buildAndPersist();
        performGetAndExpect("/organization/recruiter/all", "allRecruiters", true);
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_project() throws Exception {
        doCreateProject();
        Mockito.verify(keycloakService).createBackOfficeGroupAndRoles(ArgumentMatchers.anyString());
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_sourcing_organization_check_gdpr_mention() throws Exception {
        var title = "Sourcing entreprise without gdpr";
        var command = new SaveOrganizationCommandDTO()
                .title(title)
                .description("Entreprise sourcing test")
                .organizationType(OrganizationTypeDTO.SOURCING);

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var newRecruiter = StreamSupport.stream(recruiterRepository.findAll().spliterator(), false)
                    .filter(o -> title.equals(o.getTitle())).findFirst().orElseThrow();
            assertThat(newRecruiter.getGdprMention()).isEqualTo(DEFAULT_GDPR_MENTION);
            Mockito.verify(sourcingKeycloakService).createSourcingGroupAndRole(newRecruiter.getCode());
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_sourcing_organization_with_gdpr_mention() throws Exception {
        var mention = "custom mention";
        var code = applicationContext.getBean(RecruiterMotherObject.class).withGdprMention(mention).buildAndPersist().getCode();
        txHelper.doInTransaction(() -> {
            var newRecruiter = recruiterRepository.findOneByCode(code);
            assertThat(newRecruiter.getGdprMention()).isEqualTo(mention);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_employer() throws Exception {
        var project = organizationGenerator.createRecruiter("P-007", AbstractOrganization.OrganizationType.PROJECT);

        var command = new SaveOrganizationCommandDTO()
                .title("Employer")
                .description("A god team")
                .organizationType(OrganizationTypeDTO.EMPLOYER)
                .refererRecruiterCode(project.getCode());

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var employers = StreamSupport.stream(employerRepository.findAll().spliterator(), false)
                    .filter(e -> e.getRefererRecruiter().equals(project))
                    .toList();
            org.junit.jupiter.api.Assertions.assertAll(
                    () -> assertThat(employers).hasSize(1),
                    () -> assertThat(employers.get(0)).isInstanceOf(Employer.class)
            );
        });

        Mockito.verify(keycloakService).createBackOfficeGroupAndRoles(ArgumentMatchers.anyString());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_employer_with_long_description() throws Exception {
        var project = organizationGenerator.createRecruiter("P-007", AbstractOrganization.OrganizationType.PROJECT);

        var command = new SaveOrganizationCommandDTO()
                .title("Employer")
                .description(IntStream.range(0, 10_000).mapToObj(i -> "a").collect(Collectors.joining("")))
                .organizationType(OrganizationTypeDTO.EMPLOYER)
                .refererRecruiterCode(project.getCode());

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var employers = StreamSupport.stream(employerRepository.findAll().spliterator(), false)
                    .filter(e -> e.getRefererRecruiter().equals(project))
                    .toList();
            org.junit.jupiter.api.Assertions.assertAll(
                    () -> assertThat(employers).hasSize(1),
                    () -> assertThat(employers.get(0).getDescription()).hasSize(10_000)
            );
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void add_project_to_organization() throws Exception {

        var project = organizationGenerator.createRecruiter("P-007", AbstractOrganization.OrganizationType.PROJECT);
        var command = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.CONSORTIUM)
                .title("title")
                .description("description")
                .projectIds(Collections.singletonList(project.getId()));

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        Mockito.verify(keycloakService).createBackOfficeGroupAndRoles(ArgumentMatchers.anyString(), ArgumentMatchers.eq(project.getCode()));

    }

    private Recruiter doCreateProject() throws Exception {
        var expectedTitle = "Project " + UUID.randomUUID();
        var expectedDescription = "Descr Project " + UUID.randomUUID();
        var project = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.PROJECT)
                .title(expectedTitle)
                .description(expectedDescription);

        performPut("/organization", project)
                .andExpect(status().isNoContent());

        var created = StreamSupport.stream(recruiterRepository.findAll().spliterator(), false)
                .filter(o -> o.getOrganizationType() == AbstractOrganization.OrganizationType.PROJECT)
                .findFirst()
                .orElseThrow();
        assertThat(created.getTitle()).isEqualTo(expectedTitle);
        assertThat(created.getDescription()).isEqualTo(expectedDescription);
        return created;
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_project_with_referer() throws Exception {
        var project = organizationGenerator.createRecruiter("P-008", AbstractOrganization.OrganizationType.PROJECT);

        var command = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.EMPLOYER)
                .title("Pi")
                .description("Po")
                .refererRecruiterCode(organizationGenerator.createRecruiter().getCode())
                .id(project.getId());

        performPut("/organization", command)
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_recruiter_with_url() throws Exception {
        var recruiter = organizationGenerator.createRecruiter("P-008", AbstractOrganization.OrganizationType.TERRITORIAL);

        var command = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.PROJECT)
                .mandatoryIdentity(true)
                .title("orgaName")
                .forcedUrl("jenesuisPasunCV.fr")
                .description("Super orga")
                .id(recruiter.getId());

        performPut("/organization", command).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var refreshedRecruiter = recruiterRepository.findOneByCode(recruiter.getCode());
            assertThat(refreshedRecruiter.getUrl()).isEqualTo("jenesuisPasunCV.fr");
            assertThat(refreshedRecruiter.isForcedUrl()).isTrue();
        });
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_project_with_private_users() throws Exception {
        var project = organizationGenerator.createRecruiter("P-008", AbstractOrganization.OrganizationType.PROJECT);

        var command = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.PROJECT)
                .privateUsers(true)
                .privateJobs(true)
                .title("Pi")
                .description("Po")
                .id(project.getId());

        performPut("/organization", command).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            assertThat(recruiterRepository.findOneByCode(project.getCode()).isPrivateUsers()).isTrue();
            assertThat(recruiterRepository.findOneByCode(project.getCode()).isPrivateJobs()).isTrue();
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_project_with_mandatory_identity() throws Exception {
        var project = organizationGenerator.createRecruiter("P-008", AbstractOrganization.OrganizationType.PROJECT);

        var command = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.PROJECT)
                .mandatoryIdentity(true)
                .title("Pi")
                .description("Po")
                .id(project.getId());

        performPut("/organization", command).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            assertThat(recruiterRepository.findOneByCode(project.getCode()).isMandatoryIdentity()).isTrue();
        });
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @ParameterizedTest
    @EnumSource(value = AbstractOrganization.OrganizationType.class, names = {"CONSORTIUM", "TERRITORIAL"})
    void update_organization_with_default_project(AbstractOrganization.OrganizationType param) throws Exception {
        var project = organizationGenerator.createRecruiter("P-008", AbstractOrganization.OrganizationType.PROJECT);
        var expectedDefaultProject = organizationGenerator.createRecruiter("P-009", AbstractOrganization.OrganizationType.PROJECT);
        var ref = organizationGenerator.createRecruiter("X-001", param);

        keycloakService.createBackOfficeGroupAndRoles("X-001", "P-008", "P-009");

        var command = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.valueOf(param.name()))
                .privateUsers(false)
                .privateJobs(false)
                .title(project.getTitle())
                .description(project.getDescription())
                .defaultProjectId(expectedDefaultProject.getId())
                .id(ref.getId());

        performPut("/organization", command).andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            assertThat(recruiterRepository.findOneByCode(ref.getCode()).getDefaultProject()).extracting(AbstractOrganization::getCode).isEqualTo(expectedDefaultProject.getCode());
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_recruiter_of_employer() throws Exception {
        var firstReferer = organizationGenerator.createRecruiter("P-008", AbstractOrganization.OrganizationType.PROJECT);
        var secondReferer = organizationGenerator.createRecruiter();
        var employer = organizationGenerator.createEmployer("M-008", firstReferer);

        var updateCommand = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.EMPLOYER)
                .title("Pi")
                .description("Po")
                .refererRecruiterCode(secondReferer.getCode())
                .id(employer.getId());

        performPut("/organization", updateCommand)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedEmployer = employerRepository.findOneByCode(employer.getCode());
            assertThat(updatedEmployer.getRefererRecruiter().getCode()).isEqualTo(secondReferer.getCode());
        });
    }

    @SneakyThrows
    private void set_OT_projects(Recruiter ot, List<Recruiter> projects, Recruiter defaultProject) {
        var updateCommand = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.TERRITORIAL)
                .title("Pi")
                .description("Po")
                .id(ot.getId());

        if (projects != null && !projects.isEmpty()) {
            updateCommand.setProjectIds(projects.stream().map(AbstractEntity::getId).toList());
        }

        if (defaultProject != null) {
            updateCommand.setDefaultProjectId(defaultProject.getId());
        }

        performPut("/organization", updateCommand)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedOT = recruiterRepository.findById(updateCommand.getId()).orElseThrow();
            assertThat(updatedOT.getDefaultProject().getCode()).isEqualTo(defaultProject.getCode());
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    @SneakyThrows
    void get_recruiter_with_default_project() {
        var code = "T-008";
        txHelper.doInTransaction(() -> {
            var ot = organizationGenerator.createRecruiter(code, AbstractOrganization.OrganizationType.TERRITORIAL);
            var project = organizationGenerator.createRecruiter("P-008", AbstractOrganization.OrganizationType.PROJECT);
            set_OT_projects(ot, List.of(project), project);
            ot.setAddress("adresse de l'orga");
            ot.setForcedUrl("http://forced.url");
            ot.setSiret("123");
        });

        mvc.perform(get("/api/odas/organization/for-code/%s".formatted(code)))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("recruiterWithDefaultProject"));

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_employer_with_consortiums() throws Exception {
        var project = organizationGenerator.createRecruiter("P-1", AbstractOrganization.OrganizationType.PROJECT);
        var consortium1 = organizationGenerator.createRecruiter("C-1", AbstractOrganization.OrganizationType.CONSORTIUM);
        var consortium2 = organizationGenerator.createRecruiter("C-2", AbstractOrganization.OrganizationType.CONSORTIUM);

        var command = new SaveOrganizationCommandDTO()
                .title("Employer")
                .description("A god team")
                .organizationType(OrganizationTypeDTO.EMPLOYER)
                .consortiumIds(List.of(consortium1.getId(), consortium2.getId()))
                .refererRecruiterCode(project.getCode());

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var employers = StreamSupport.stream(employerRepository.findAll().spliterator(), false)
                    .filter(e -> e.getRefererRecruiter().equals(project))
                    .toList();
            org.junit.jupiter.api.Assertions.assertAll(
                    () -> assertThat(employers).hasSize(1),
                    () -> assertThat(employers.get(0)).isInstanceOf(Employer.class),
                    () -> assertThat(employers.get(0).getConsortiums()).hasSize(2),
                    () -> assertThat(employers.get(0).getConsortiums().stream().map(AbstractOrganization::getId))
                            .contains(consortium1.getId(), consortium2.getId())
            );
        });

        Mockito.verify(keycloakService).createBackOfficeGroupAndRoles(ArgumentMatchers.anyString());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void get_employer_with_consortiums() throws Exception {
        var project = organizationGenerator.createRecruiter("P-1", AbstractOrganization.OrganizationType.PROJECT);
        var consortium1 = organizationGenerator.createRecruiter("C-1", AbstractOrganization.OrganizationType.CONSORTIUM);
        var consortium2 = organizationGenerator.createRecruiter("C-2", AbstractOrganization.OrganizationType.CONSORTIUM);
        var employer = organizationGenerator.createEmployer("M-1", project, consortium1, consortium2);

        mvc.perform(get("/api/odas/organization/for-code/%s".formatted(employer.getCode())))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("employer"));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void get_employer_private_users() throws Exception {
        var code = "P-1";
        var project = organizationGenerator.createRecruiter(code, AbstractOrganization.OrganizationType.PROJECT, true, true, false);

        mvc.perform(get(realUrl("/organization/for-code/%s".formatted(code))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.privateUsers").value(true))
                .andExpect(jsonPath("$.privateJobs").value(true));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void get_employer_mandatory_identity() throws Exception {
        var code = "P-1";
        var project = organizationGenerator.createRecruiter(code, AbstractOrganization.OrganizationType.PROJECT, false, false, true);

        mvc.perform(get(realUrl("/organization/for-code/%s".formatted(code))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.mandatoryIdentity").value(true));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_employer_with_email_template_will_fail() throws Exception {
        var project = organizationGenerator.createRecruiter("P-1", AbstractOrganization.OrganizationType.PROJECT);

        var command = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.EMPLOYER)
                .title("title")
                .description("description")
                .refererRecruiterCode(organizationGenerator.createRecruiter().getCode())
                .id(project.getId())
                .refusalEmailTemplate(createRefusalEmailTemplate());

        performPut("/organization", command)
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_recruiter_with_email_template() throws Exception {
        var code = "R-0015";
        var refusalEmail = createRefusalEmailTemplate();
        var recruiter = organizationGenerator.createRecruiter(code, AbstractOrganization.OrganizationType.TERRITORIAL);

        var updateCommand = new SaveOrganizationCommandDTO()
                .organizationType(OrganizationTypeDTO.TERRITORIAL)
                .title("Pi")
                .description("Po")
                .id(recruiter.getId())
                .refusalEmailTemplate(refusalEmail);

        performPut("/organization", updateCommand)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedRecruiter = recruiterRepository.findOneByCode(code);
            assertThat(customEmailTemplateDTOBuilder.buildDTO(updatedRecruiter.getRefusalEmailTemplate())).isEqualTo(refusalEmail);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void get_recruiters_list_from_organization() throws Exception {
        var organization = organizationGenerator.createRecruiter("T-01", AbstractOrganization.OrganizationType.TERRITORIAL);
        var project = organizationGenerator.createRecruiter("P-01", AbstractOrganization.OrganizationType.PROJECT, false, true, false);

        keycloakService.createBackOfficeGroupAndRoles(organization.getCode(), project.getCode());
        keycloakService.createBackOfficeGroupAndRoles(organizationGenerator.createRecruiter("T-02").getCode());

        userProfileGenerator.createUserProfile(UUID.randomUUID(), organization.getCode());

        mvc.perform(get(ApiConstants.API_ODAS_ORGANIZATION + ApiConstants.SEPARATOR + "all-recruiters-for-organization")
                        .param("organizationCode", organization.getCode())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContentWithOrderedArray("allRecruitersForOrga"))
                .andExpect(jsonPath("$[*].id", containsInAnyOrder(organization.getId().intValue(), project.getId().intValue())))
                .andExpect(jsonPath("$[*].code", containsInAnyOrder(organization.getCode(), project.getCode())));
    }

    private CustomEmailTemplateDTO createRefusalEmailTemplate() {
        return new CustomEmailTemplateDTO().emailFrom("<EMAIL>").subject("Refusal email").content("You are refused").authorAlias("Iris");
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_recruiter_with_diffusion_type() throws Exception {
        var command = new SaveOrganizationCommandDTO()
                .title("Recruiter with diffusion type")
                .description("A recruiter with diffusion type")
                .organizationType(OrganizationTypeDTO.SOURCING)
                .defaultDiffusionType(DiffusionTypeDTO.CV);

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var recruiters = Lists.newArrayList(recruiterRepository.findAll()).stream().filter(r -> r.getTitle().equals("Recruiter with diffusion type")).toList();
            assertThat(recruiters).hasSize(1);
            assertThat(recruiters.getFirst().getDefaultDiffusionType()).isEqualTo(DiffusionType.CV);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_recruiter_diffusion_type() throws Exception {
        var recruiter = organizationGenerator.createRecruiter("R-DIFF", AbstractOrganization.OrganizationType.SOURCING);

        txHelper.doInTransaction(() -> {
            var initialRecruiter = recruiterRepository.findById(recruiter.getId()).orElseThrow();
            assertThat(initialRecruiter.getDefaultDiffusionType()).isEqualTo(DiffusionType.BOTH);
        });

        var command = new SaveOrganizationCommandDTO()
                .id(recruiter.getId())
                .title(recruiter.getTitle())
                .description(recruiter.getDescription())
                .organizationType(OrganizationTypeDTO.SOURCING)
                .defaultDiffusionType(DiffusionTypeDTO.HANDICAP);

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedRecruiter = recruiterRepository.findById(recruiter.getId()).orElseThrow();
            assertThat(updatedRecruiter.getDefaultDiffusionType()).isEqualTo(DiffusionType.HANDICAP);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_recruiter_diffusion_type_should_reindex_recruitments() throws Exception {
        var recruiter = organizationGenerator.createRecruiter("R-DIFF-REINDEX", AbstractOrganization.OrganizationType.SOURCING);

        txHelper.doInTransaction(() -> {
            applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(recruiter).withRecruiterType(AbstractOrganization.OrganizationType.SOURCING).withState(RecruitmentState.PUBLISHED).buildAndPersist();
            applicationContext.getBean(RecruitmentMotherObject.class).withRecruiter(recruiter).withRecruiterType(AbstractOrganization.OrganizationType.SOURCING).withState(RecruitmentState.PUBLISHED).buildAndPersist();
        });

        Mockito.reset(recruitmentIndexer);

        var command = new SaveOrganizationCommandDTO()
                .id(recruiter.getId())
                .title(recruiter.getTitle())
                .description(recruiter.getDescription())
                .organizationType(OrganizationTypeDTO.SOURCING)
                .defaultDiffusionType(DiffusionTypeDTO.CV);

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var recruitments = recruitmentRepository.findRecruitmentToIndexByRecruiter(recruiter);
            assertThat(recruitments).hasSize(2);
            Mockito.verify(recruitmentIndexer).indexRecruitmentsForRecruiter(Mockito.any(Recruiter.class));
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void get_recruiter_with_diffusion_type() throws Exception {
        var code = "R-DIFF-GET";
        var recruiter = organizationGenerator.createRecruiter(code, AbstractOrganization.OrganizationType.SOURCING);

        txHelper.doInTransaction(() -> {
            var r = recruiterRepository.findById(recruiter.getId()).orElseThrow();
            r.setDefaultDiffusionType(DiffusionType.CV);
            recruiterRepository.save(r);
        });

        mvc.perform(get(realUrl("/organization/for-code/%s".formatted(code))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.defaultDiffusionType").value("CV"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_recruiter_with_none_diffusion_type() throws Exception {
        var recruiter = organizationGenerator.createRecruiter("R-DIFF-NONE", AbstractOrganization.OrganizationType.SOURCING);

        var command = new SaveOrganizationCommandDTO()
                .id(recruiter.getId())
                .title(recruiter.getTitle())
                .description(recruiter.getDescription())
                .organizationType(OrganizationTypeDTO.SOURCING)
                .defaultDiffusionType(DiffusionTypeDTO.NONE);

        performPut("/organization", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedRecruiter = recruiterRepository.findById(recruiter.getId()).orElseThrow();
            assertThat(updatedRecruiter.getDefaultDiffusionType()).isEqualTo(DiffusionType.NONE);
        });

        Mockito.verify(recruitmentIndexer, Mockito.times(1)).indexRecruitmentsForRecruiter(Mockito.any(Recruiter.class));
    }
}
