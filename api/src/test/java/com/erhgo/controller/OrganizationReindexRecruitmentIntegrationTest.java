package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.search.recruitment.RecruitmentIndexer;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

class OrganizationReindexRecruitmentIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private ApplicationContext applicationContext;

    @SpyBean
    private RecruitmentIndexer recruitmentIndexer;

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void reindex_recruiter_recruitments() {
        var recruiter = organizationGenerator.createRecruiter("R-TEST-REINDEX", AbstractOrganization.OrganizationType.SOURCING);

        applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiter(recruiter)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiter(recruiter)
                .buildAndPersist();

        Mockito.reset(recruitmentIndexer);

        performPost("/organization/%s/reindex-recruitments".formatted(recruiter.getCode()), null)
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        Mockito.verify(recruitmentIndexer, Mockito.times(1)).indexRecruitmentsForRecruiter(Mockito.any(Recruiter.class));
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void reindex_recruiter_recruitments_not_found() {
        performPost("/organization/NON-EXISTENT/reindex-recruitments", null)
                .andExpect(MockMvcResultMatchers.status().isNotFound());

        Mockito.verify(recruitmentIndexer, Mockito.never()).indexRecruitmentsForRecruiter(Mockito.any(Recruiter.class));
    }
}
