package com.erhgo.controller.generation;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.PromptConfig;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationState;
import com.erhgo.domain.enums.DriverLicence;
import com.erhgo.domain.enums.FileImportState;
import com.erhgo.domain.enums.HardSkillType;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.UserRegistrationState;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.openapi.dto.ErhgoOccupationMinimumInfoDTO;
import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.FindBestMatchingOccupationService;
import com.erhgo.services.generation.TitleGenerationService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.ResumeBackuperService;
import com.erhgo.services.search.ErhgoOccupationFinder;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import com.erhgo.services.userprofile.FilePartProvider;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.stubbing.OngoingStubbing;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ExperiencesFromCVGenerationControllerTest extends AbstractIntegrationTest {
    @MockBean
    GenerationClient generationClient;

    @MockBean
    FindBestMatchingOccupationService findBestMatchingOccupationService;

    @MockBean
    KeycloakMockService keycloakService;

    @MockBean
    private ErhgoOccupationIndexer erhgoOccupationIndexerMock;

    @MockBean
    @SuppressWarnings("unused")
    private ErhgoOccupationFinder erhgoOccupationFinder;

    @Autowired
    private ApplicationContext applicationContext;

    @MockBean
    TitleGenerationService titleGenerationService;

    @MockBean
    private ResumeBackuperService resumeBackuperService;

    static final String USER_ID = "56b64e21-4771-4d01-8cf7-696866d3ae49";


    @BeforeEach
    @SneakyThrows
    void init() {
        when(titleGenerationService.normalizeTitle(any()))
                .thenReturn(new OpenAIResponse<NormalizedTitlesResponse>()
                        .setResult(new NormalizedTitlesResponse("title M", "title F")
                        ));
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    @SneakyThrows
    void correct_json_will_generate_appropriate_experiences() {
        var pdfFile = Objects.requireNonNull(TestUtils.class.getResourceAsStream("/data/cv.pdf")).readAllBytes();
        prepareCVRead();

        when(geoService.fetchGeoCoordinates(any(), any())).thenReturn(new LocationDTO()
                .city("Lyon")
                .latitude(48.6231673F)
                .longitude(7.7118232F));

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));

        when(keycloakService.getFrontOfficeUserProfile(anyString())).thenReturn(Optional.of(new UserRepresentation().setEmail("email@localhost")));

        applicationContext.getBean(CriteriaMotherObject.class)
                .forDriverLicence()
                .buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        mvc.perform(multipart(realUrl("/user/%s/generate-experiences-from-cv".formatted(userProfile.userId())))
                        .file(new MockMultipartFile(
                                "file",
                                "cv.pdf",
                                "application/pdf",
                                pdfFile
                        ))
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isNoContent());

        verify(keycloakService).updateFOEmailAndUsername(USER_ID, "email@localhost", "John", "Doe");
        verify(resumeBackuperService, times(1)).backupResume(eq(USER_ID), any(FilePartProvider.class));

        txHelper.doInTransaction(() -> {
            var user = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow();
            assertThat(user.experiences()).hasSize(2);
            assertThat(user.getFileImportState()).isEqualTo(FileImportState.COMPLETED);
            assertThat(user.registrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.CREATED_FROM_RESUME);
            assertThat(user.getPhoneNumber()).isEqualTo("**********");
            assertThat(user.getDriverLicenceFromCriteria()).isEqualTo(DriverLicence.LICENCE_B);
            assertThat(user.generalInformation().getLocation().getCity()).isEqualTo("Lyon");
            assertThat(user.experiences()).extracting(UserExperience::getErhgoOccupation).allSatisfy(o -> {
                Assertions.assertThat(o.getAlternativeLabels()).hasSize(1).allMatch("title F"::equals);
                Assertions.assertThat(o.getTitle()).isEqualTo("title M");
            });
        });
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    @SneakyThrows
    void correct_image_will_generate_appropriate_experiences() {
        var imageFile = Objects.requireNonNull(TestUtils.class.getResourceAsStream("/data/cv.png")).readAllBytes();
        prepareCVRead();

        when(geoService.fetchGeoCoordinates(any(), any())).thenReturn(new LocationDTO()
                .city("Lyon")
                .latitude(48.6231673F)
                .longitude(7.7118232F));

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));

        when(keycloakService.getFrontOfficeUserProfile(anyString())).thenReturn(Optional.of(new UserRepresentation().setEmail("email@localhost")));

        applicationContext.getBean(CriteriaMotherObject.class)
                .forDriverLicence()
                .buildAndPersist();

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        mvc.perform(multipart(realUrl("/user/%s/generate-experiences-from-cv".formatted(userProfile.userId())))
                        .file(new MockMultipartFile(
                                "file",
                                "cv.pdf",
                                "image/png",
                                imageFile
                        ))
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isNoContent());

        verify(keycloakService).updateFOEmailAndUsername(USER_ID, "email@localhost", "John", "Doe");

        verify(resumeBackuperService, times(1)).backupResume(eq(USER_ID), any(FilePartProvider.class));

        txHelper.doInTransaction(() -> {
            var user = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow();
            assertThat(user.experiences()).hasSize(2);
            assertThat(user.getFileImportState()).isEqualTo(FileImportState.COMPLETED);
            assertThat(user.registrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.CREATED_FROM_RESUME);
            assertThat(user.getPhoneNumber()).isEqualTo("**********");
            assertThat(user.getDriverLicenceFromCriteria()).isEqualTo(DriverLicence.LICENCE_B);
            assertThat(user.getHardSkills()).containsKeys(HardSkillType.values());
            assertThat(user.generalInformation().getLocation().getCity()).isEqualTo("Lyon");
        });
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    @SneakyThrows
    void duplicate_experience_will_create_occupation_and_not_add_label() {
        var generatedTitle = "Titre génére (F / H / NB)";
        var occupationId = UUID.randomUUID();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withId(occupationId)
                .withTitle("Responsable Commercial")
                .buildAndPersist();

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO().title(occupation.getTitle()).id(occupationId))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));

        var pdfFile = Objects.requireNonNull(TestUtils.class.getResourceAsStream("/data/cv.pdf")).readAllBytes();
        prepareCVRead()
                .thenReturn(new ChatCompletionResponse(generatedTitle, null))
        ;

        when(keycloakService.getFrontOfficeUserProfile(anyString())).thenReturn(Optional.of(new UserRepresentation().setEmail("email@localhost")));

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        mvc.perform(multipart(realUrl("/user/%s/generate-experiences-from-cv".formatted(userProfile.userId())))
                        .file(new MockMultipartFile(
                                "file",
                                "cv.pdf",
                                "application/pdf",
                                pdfFile
                        ))
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isNoContent());

        verify(erhgoOccupationIndexerMock, times(1)).updateOccupationIndexation(any(ErhgoOccupation.class));

        txHelper.doInTransaction(() -> {
            var user = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow();
            assertThat(user.experiences()).hasSize(2);
            var previousOccupation = user.experiences().stream()
                    .filter(x -> x.getJobTitle().equals(occupation.getTitle())).findFirst().orElseThrow()
                    .getErhgoOccupation();
            assertThat(previousOccupation).isNotNull();
            assertThat(previousOccupation.getAlternativeLabels()).isEmpty();

            var newOccupation = user.experiences().stream()
                    .filter(x -> !x.getJobTitle().equals(occupation.getTitle())).findFirst().orElseThrow()
                    .getErhgoOccupation();
            assertThat(newOccupation).isNotNull();
            assertThat(newOccupation.getQualificationState()).isEqualTo(ErhgoOccupationState.TO_CONFIRM);
            assertThat(user.getFileImportState()).isEqualTo(FileImportState.COMPLETED);
        });
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    @SneakyThrows
    void twice_import_will_trigger_rate_limit_exception() {
        var pdfFile = Objects.requireNonNull(TestUtils.class.getResourceAsStream("/data/cv.pdf")).readAllBytes();
        prepareCVRead();

        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());

        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));

        when(keycloakService.getFrontOfficeUserProfile(anyString())).thenReturn(Optional.of(new UserRepresentation().setEmail("email@localhost")));

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        mvc.perform(multipart(realUrl("/user/%s/generate-experiences-from-cv".formatted(userProfile.userId())))
                        .file(new MockMultipartFile(
                                "file",
                                "cv.pdf",
                                "application/pdf",
                                pdfFile
                        ))
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isNoContent());

        mvc.perform(multipart(realUrl("/user/%s/generate-experiences-from-cv".formatted(userProfile.userId())))
                        .file(new MockMultipartFile(
                                "file",
                                "cv.pdf",
                                "application/pdf",
                                pdfFile
                        ))
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isTooManyRequests());
    }

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    @SneakyThrows
    void occupation_created_from_buildAndSaveUserExperiences_has_state_TO_CONFIRM() {
        when(findBestMatchingOccupationService.findSimilarLabel(any(FindBestMatchingOccupationArguments.class)))
                .thenReturn(new ErhgoOccupationMinimumInfoDTO())
                .thenReturn(new ErhgoOccupationMinimumInfoDTO());
        when(erhgoOccupationIndexerMock.updateOccupationIndexation(any())).thenReturn(CompletableFuture.completedFuture(null));

        var pdfFile = Objects.requireNonNull(TestUtils.class.getResourceAsStream("/data/cv.pdf")).readAllBytes();
        prepareCVRead();

        when(keycloakService.getFrontOfficeUserProfile(anyString())).thenReturn(Optional.of(new UserRepresentation().setEmail("email@localhost")));

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .buildAndPersist();

        mvc.perform(multipart(realUrl("/user/%s/generate-experiences-from-cv".formatted(userProfile.userId())))
                        .file(new MockMultipartFile(
                                "file",
                                "cv.pdf",
                                "application/pdf",
                                pdfFile
                        ))
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isNoContent());

        assertThat(erhgoOccupationIndexerMock.updateOccupationIndexation(any(ErhgoOccupation.class)).join()).isNull();

        txHelper.doInTransaction(() -> {
            var user = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow();
            assertThat(user.experiences()).extracting(UserExperience::getErhgoOccupation).allMatch(p -> p.getQualificationState() == ErhgoOccupationState.TO_CONFIRM).hasSize(2);
            assertThat(user.getFileImportState()).isEqualTo(FileImportState.COMPLETED);
            assertThat(user.registrationStep()).isEqualTo(UserRegistrationState.RegistrationStep.CREATED_FROM_RESUME);
        });
    }

    private OngoingStubbing<ChatCompletionResponse> prepareCVRead() throws IOException {
        var hardSkillsJson = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/hardSkillsJson.json"));
        var experiencesJson = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/experiencesFromCV.json"));
        var titlesJson = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/normalizedTitleFromGeneratedExperiencesFromCV.json"));
        var userInfosJson = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("data/userInfosFromCV.json"));
        return when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class)))
                .thenReturn(new ChatCompletionResponse(hardSkillsJson, null))
                .thenReturn(new ChatCompletionResponse(userInfosJson, null))
                .thenReturn(new ChatCompletionResponse(experiencesJson, null))
                .thenReturn(new ChatCompletionResponse(titlesJson, null))
                ;
    }
}
