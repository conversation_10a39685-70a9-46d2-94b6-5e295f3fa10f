package com.erhgo.services.externaloffer.candidature;

import com.erhgo.services.mailing.GMailNotifier;
import com.erhgo.services.userprofile.FilePartProvider;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.model.Message;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class DirectMailNotifierUnitTest {

    Gmail mockedGmail;
    Gmail.Users mockedGmailUsers;
    Gmail.Users.Messages mockedGmailMessage;
    Gmail.Users.Messages.Send mockedSend;

    @BeforeEach
    void init() {
        mockedGmail = Mockito.mock(Gmail.class);
        mockedGmailUsers = Mockito.mock(Gmail.Users.class);
        mockedGmailMessage = Mockito.mock(Gmail.Users.Messages.class);
        mockedSend = Mockito.mock(Gmail.Users.Messages.Send.class);
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(strings = {"default", "staging", "master"})
    void sendMail(String target) {
        var senderEmail = "s";
        var applicationName = "an";
        var to = "to";
        var from = "from";
        var subject = "subject";
        var body = "body";
        var env = Mockito.mock(Environment.class);
        when(env.getActiveProfiles()).thenReturn(new String[]{target});
        var service = new GMailNotifier(env);
        ReflectionTestUtils.setField(service, "gmailService", mockedGmail);
        ReflectionTestUtils.setField(service, "senderEmail", senderEmail);
        ReflectionTestUtils.setField(service, "applicationName", applicationName);

        when(mockedGmail.users()).thenReturn(mockedGmailUsers);
        when(mockedGmailUsers.messages()).thenReturn(mockedGmailMessage);
        when(mockedGmailMessage.send(anyString(), any(Message.class))).thenReturn(mockedSend);
        when(mockedSend.execute()).thenReturn(new Message());

        service.sendMail(Set.of(to), from, null, false, subject, body, new FilePartProvider("nothing".getBytes(), "fn", "application/pdf"));

        Mockito.verify(mockedGmailMessage).send(eq(senderEmail), Mockito.assertArg(m -> {
            var raw = new String(m.decodeRaw());
            Assertions.assertThat(raw)
                    .contains("From: %s".formatted(senderEmail))
                    .contains("Reply-To: %s".formatted(from))
                    .contains("Subject: %s".formatted("master".equals(target) ? subject : "[Test"))
                    .contains("To: %s".formatted(to));
        }));
    }
}
