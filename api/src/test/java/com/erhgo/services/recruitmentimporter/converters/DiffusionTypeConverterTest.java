package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.domain.enums.DiffusionType;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class DiffusionTypeConverterTest {

    private final DiffusionTypeConverter converter = new DiffusionTypeConverter();

    @Test
    void convert_whenValueIsCV_shouldReturnCV() throws CsvConstraintViolationException, CsvDataTypeMismatchException {
        assertThat(converter.convert("CV")).isEqualTo(DiffusionType.CV);
        assertThat(converter.convert("cv")).isEqualTo(DiffusionType.CV);
        assertThat(converter.convert(" CV ")).isEqualTo(DiffusionType.CV);
    }

    @Test
    void convert_whenValueIsHANDICAP_shouldReturnHANDICAP() throws CsvConstraintViolationException, CsvDataTypeMismatchException {
        assertThat(converter.convert("HANDICAP")).isEqualTo(DiffusionType.HANDICAP);
        assertThat(converter.convert("handicap")).isEqualTo(DiffusionType.HANDICAP);
        assertThat(converter.convert(" HANDICAP ")).isEqualTo(DiffusionType.HANDICAP);
    }

    @Test
    void convert_whenValueIsAUCUN_shouldReturnNONE() throws CsvConstraintViolationException, CsvDataTypeMismatchException {
        assertThat(converter.convert("AUCUN")).isEqualTo(DiffusionType.NONE);
        assertThat(converter.convert("aucun")).isEqualTo(DiffusionType.NONE);
        assertThat(converter.convert(" AUCUN ")).isEqualTo(DiffusionType.NONE);
    }

    @Test
    void convert_whenValueIsOther_shouldReturnBOTH() throws CsvConstraintViolationException, CsvDataTypeMismatchException {
        assertThat(converter.convert("")).isEqualTo(DiffusionType.BOTH);
        assertThat(converter.convert(null)).isEqualTo(DiffusionType.BOTH);
        assertThat(converter.convert("BOTH")).isEqualTo(DiffusionType.BOTH);
        assertThat(converter.convert("autre")).isEqualTo(DiffusionType.BOTH);
        assertThat(converter.convert("123")).isEqualTo(DiffusionType.BOTH);
    }
}
