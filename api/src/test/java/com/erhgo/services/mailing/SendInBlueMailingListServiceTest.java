package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.UserRegistrationState;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.notifier.Notifier;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;
import sendinblue.ApiException;
import sibApi.ContactsApi;
import sibApi.SendersApi;
import sibApi.TransactionalEmailsApi;
import sibModel.*;

import java.util.List;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@TestPropertySource(properties = {"sendinblue.apiKey=42", "sendinblue.retry-delay=50"})
class SendInBlueMailingListServiceTest extends AbstractIntegrationTest {

    private static final String EMAIL = "<EMAIL>";
    private static final String USER_ID = "42";
    private static final Long JOB_DATING_OPTIN_ID = 63L;

    @MockBean
    SendInBlueClientConfiguration sendInBlueClientConfiguration;

    @SpyBean
    SendInBlueMailingListService sendInBlueMailingListService;

    @Mock
    ContactsApi contactsApi;

    @Mock
    TransactionalEmailsApi transactionalEmailsApi;

    @Mock
    SendersApi sendersApi;

    GetSendersListSenders senderListSenders;
    GetSendersList getSendersList;

    @Autowired
    ApplicationContext applicationContext;

    @MockBean
    KeycloakMockService keycloakMockService;

    @MockBean
    Notifier notifier;

    @BeforeEach
    void prepareClient() throws ApiException {
        when(sendInBlueClientConfiguration.getContactApi()).thenReturn(contactsApi);
        when(sendInBlueClientConfiguration.getTransactionalEmailApi()).thenReturn(transactionalEmailsApi);
        when(sendInBlueClientConfiguration.getSendersApi()).thenReturn(sendersApi);
        when(sendInBlueClientConfiguration.getTransactionalEmailApi()).thenReturn(transactionalEmailsApi);
        when(sendInBlueClientConfiguration.getJobDatingOptinFrontUsersListId()).thenReturn(JOB_DATING_OPTIN_ID);
        senderListSenders = new GetSendersListSenders().id(1L).email("<EMAIL>").active(true);
        getSendersList = new GetSendersList().senders(List.of(senderListSenders));
        when(sendersApi.getSenders(null, null)).thenReturn(getSendersList);
    }

    @Test
    @ResetDataAfter
    void updateContact_when_bad_request_abort() throws ApiException {
        doThrow(new ApiException(400, "ko")).when(contactsApi).updateContact(eq(EMAIL), any());
        sendInBlueMailingListService.updateLocation(createUser());
        verify(contactsApi).updateContact(eq(EMAIL), any());
        verifyNoMoreInteractions(contactsApi);
    }

    @Test
    @ResetDataAfter
    @SneakyThrows
    void update_contact_experiences_does_not_provoke_lazy_init() {
        doNothing().when(contactsApi).updateContact(eq(EMAIL), any());
        var userRepresentation = new UserRepresentation().setEmail(EMAIL).setId(USER_ID);
        when(keycloakMockService.getFrontOfficeUserProfile(USER_ID)).thenReturn(Optional.of(userRepresentation));
        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withEmail(EMAIL)
                .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        var refetchedUser = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow();
        sendInBlueMailingListService.updateNumberOfExperiences(refetchedUser);
        verify(contactsApi)
                .updateContact(eq(EMAIL), argThat(a -> Integer.parseInt(((Properties) (a.getAttributes())).getProperty(SendInBlueMailingListService.NUMBER_OF_EXPERIENCES_SENDINBLUE_PROPERTY)) == 1));

    }

    @Test
    @ResetDataAfter
    @SneakyThrows
    void update_contact_location_does_not_provoke_lazy_init_on_create() {

        doThrow(new ApiException(404, "ko")).when(contactsApi).updateContact(eq(EMAIL), any());
        doReturn(new CreateUpdateContactModel()).when(contactsApi).createContact(any());
        var userRepresentation = new UserRepresentation().setEmail(EMAIL).setId(USER_ID);
        when(keycloakMockService.getFrontOfficeUserProfile(USER_ID)).thenReturn(Optional.of(userRepresentation));
        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withEmail(EMAIL)
                .withOptOutMail("<EMAIL>")
                .withSalary(10000)
                .withLocation(Location.builder().city("a").postcode("59000").regionName("Nord").departmentCode("59").build())
                .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist())
                .buildAndPersist();

        var refetchedUser = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow();
        sendInBlueMailingListService.updateNumberOfExperiences(refetchedUser);
        verify(contactsApi)
                .updateContact(
                        eq(EMAIL),
                        argThat(a ->
                                {
                                    var p = (Properties) a.getAttributes();
                                    var nbXpStr = p.getProperty(SendInBlueMailingListService.NUMBER_OF_EXPERIENCES_SENDINBLUE_PROPERTY);
                                    return Integer.parseInt(nbXpStr) == 1;
                                }
                        ));
        verify(contactsApi)
                .createContact(
                        argThat(a ->
                                {
                                    var p = (Properties) a.getAttributes();
                                    var nbXpStr = p.getProperty(SendInBlueMailingListService.NUMBER_OF_EXPERIENCES_SENDINBLUE_PROPERTY);
                                    var cp = p.getProperty(SendInBlueMailingListService.POSTCODE_SENDINBLUE_KEY);
                                    var region = p.getProperty(SendInBlueMailingListService.REGION_SENDINBLUE_KEY);
                                    var dpt = p.getProperty(SendInBlueMailingListService.DEPARTMENT_SENDINBLUE_KEY);
                                    var salary = p.getProperty(SendInBlueMailingListService.SALARY_SENDINBLUE_PROPERTY);
                                    var lastCtx = p.getProperty(SendInBlueMailingListService.LAST_CONNECTION_DATE_SENDING_BLUE_PROPERTY);
                                    return Integer.parseInt(nbXpStr) == 1
                                            && "59000".equals(cp)
                                            && "Nord".equals(region)
                                            && "59".equals(dpt)
                                            && a.getEmail().equals(EMAIL)
                                            && lastCtx != null
                                            && "10000".equals(salary);
                                }
                        ));
    }

    @Test
    @ResetDataAfter
    @Transactional
    void updateContact_when_no_account_should_create_it() throws ApiException {
        doThrow(new ApiException(404, "ko")).when(contactsApi).updateContact(eq(EMAIL), any());
        sendInBlueMailingListService.updateLocation(createUser());

        verify(contactsApi).updateContact(eq(EMAIL), any());
        verify(contactsApi).createContact(any());
        verifyNoMoreInteractions(contactsApi);
    }

    @Test
    @ResetDataAfter
    void updateSituation_situation_text_is_correct() throws ApiException {
        var user = createUser();
        var employeeExpectedValue = 3L;
        var situationSibProperty = "SITUATION";
        sendInBlueMailingListService.updateSituation(user);
        var attributes = new Properties();
        attributes.setProperty(situationSibProperty, String.valueOf(employeeExpectedValue));
        verify(contactsApi).updateContact(EMAIL, new UpdateContact().attributes(attributes));
    }


    @Test
    @ResetDataAfter
    void updateContact_ignores_uninitialized_user() {
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withRegistrationStep(UserRegistrationState.RegistrationStep.BO_INITIALIZED)
                .buildAndPersist();

        sendInBlueMailingListService.updateLocation(user);
        verifyNoMoreInteractions(contactsApi);
    }

    private UserProfile createUser(String... optOutMails) {
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withSituation(Situation.EMPLOYEE)
                .withRegistrationStep(UserRegistrationState.RegistrationStep.BO_CONFIRMED)
                .withOptOutMail(String.join(",", optOutMails))
                .buildAndPersist();
        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(EMAIL);
        userRepresentation.setId(USER_ID);
        when(keycloakMockService.getFrontOfficeUserProfile(USER_ID)).thenReturn(Optional.of(userRepresentation));
        return userProfile;
    }

    @Test
    @ResetDataAfter
    void do_throw_500_when_SIB_failed() throws ApiException {
        doThrow(new ApiException(500, "ko")).when(contactsApi).updateContact(eq(EMAIL), any());

        try {
            sendInBlueMailingListService.updateLocation(createUser());
            fail();
        } catch (GenericTechnicalException e) {
            assertThat(e).hasCauseInstanceOf(ApiException.class);
        }

        verify(contactsApi).updateContact(eq(EMAIL), any());
        verifyNoMoreInteractions(contactsApi);
    }

    @Test
    void trim_username_to_null() throws ApiException {
        var email = "<EMAIL>";
        var subject = "s";
        var expectedSubject = "[Test] (%s)".formatted(subject);
        var from = "ff@đd.fr";
        var content = "c";
        when(transactionalEmailsApi.sendTransacEmail(any(SendSmtpEmail.class))).thenReturn(new CreateSmtpEmail());
        sendInBlueMailingListService.sendMail(email, " ", subject, content, from, "");
        verify(transactionalEmailsApi).sendTransacEmail(assertArg(s ->
                {
                    assertThat(s.getTo()).hasSize(1);
                    assertThat(s.getTo().getFirst().getEmail()).isEqualTo(email);
                    assertThat(s.getTo().getFirst().getName()).isNull();
                    assertThat(s.getSubject()).isEqualTo(expectedSubject);
                    assertThat(s.getSender().getEmail()).isEqualTo(from);
                    assertThat(s.getSender().getName()).isNull();
                    assertThat(s.getTextContent()).isEqualTo(content);
                }
        ));

    }

    @Test
    void staging_mail_has_suffix_in_subject() throws ApiException {
        var mockedEnv = mock(Environment.class);
        var activeProfiles = new String[]{"staging", "default"};
        var email = "<EMAIL>";
        var subject = "s";
        var from = "fr@đd.fr";
        var content = "c";
        var expectedSubject = "[Test - STAGING] (%s)".formatted(subject);

        when(mockedEnv.getActiveProfiles()).thenReturn(activeProfiles);
        ReflectionTestUtils.setField(sendInBlueMailingListService, "environment", mockedEnv);

        when(transactionalEmailsApi.sendTransacEmail(any(SendSmtpEmail.class))).thenReturn(new CreateSmtpEmail());
        sendInBlueMailingListService.sendMail(email, " ", subject, content, from, "");
        verify(transactionalEmailsApi).sendTransacEmail(argThat(s -> s.getSubject().equals(expectedSubject)));

    }

    @Test
    void does_ont_send_filtered_email() {
        sendInBlueMailingListService.sendMail("<EMAIL>", " ", "subject", " content", "from", "");
        verifyNoInteractions(transactionalEmailsApi);
    }


    @Test
    void set_username() throws ApiException {
        var senderAlias = "moi";
        var recipientAlias = "toi";
        when(transactionalEmailsApi.sendTransacEmail(any(SendSmtpEmail.class))).thenReturn(new CreateSmtpEmail());
        sendInBlueMailingListService.sendMail("<EMAIL>", recipientAlias, "subject", "content", " from", senderAlias);
        verify(transactionalEmailsApi).sendTransacEmail(argThat(s ->
                s.getTo().size() == 1
                        && s.getTo().get(0).getName().equals(recipientAlias)
                        && s.getSender().getName().equals(senderAlias)
        ));
    }

    @Test
    @ResetDataAfter
    @Transactional
    void updateEmail_when_not_found_do_create() throws ApiException {
        var oldEmail = "<EMAIL>";
        var newMail = "<EMAIL>";
        var user = createUser();
        mockGetUserRepresentationByEmail(user.userId());

        doThrow(new ApiException(404, "ko")).when(contactsApi).updateContact(eq(oldEmail), any());
        doThrow(new ApiException(404, "not found")).when(contactsApi).getContactInfo(eq(newMail), any(), any());

        sendInBlueMailingListService.processFOEmailUpdate(oldEmail, newMail);

        verify(contactsApi).updateContact(eq(oldEmail), any());
        verify(contactsApi).createContact(any());
    }

    @Test
    @ResetDataAfter
    @Transactional
    void processEmailUpdate_when_new_email_already_exists() throws ApiException {
        var oldEmail = "<EMAIL>";
        var newMail = "<EMAIL>";
        var user = createUser();
        mockGetUserRepresentationByEmail(user.userId());
        mockGetUserRepresentationByUserId(user.userId(), newMail);

        when(contactsApi.getContactInfo(newMail, null, null)).thenReturn(new GetExtendedContactDetails());

        sendInBlueMailingListService.processFOEmailUpdate(oldEmail, newMail);

        verify(contactsApi).getContactInfo(newMail, null, null);
        verify(contactsApi).deleteContact(oldEmail);
        verify(contactsApi, Mockito.times(2)).updateContact(eq(newMail), any());
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @Transactional
    void addContactToList_create_if_required() {
        doThrow(new ApiException(404, "ko")).when(contactsApi).updateContact(eq(EMAIL), any());
        sendInBlueMailingListService.updateNewsOptIn(createUser(), true);
        verify(contactsApi).updateContact(eq(EMAIL), any());
        verify(contactsApi).createContact(argThat(a -> a.getEmail().equals(EMAIL) && !a.getListIds().isEmpty()));
        verifyNoMoreInteractions(contactsApi);
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @Transactional
    void addContactToList_create_then_update_phone() {
        var user = createUser();
        user.generalInformation().setPhoneNumber("0123456789");
        doThrow(new ApiException(404, "ko")).doNothing().when(contactsApi).updateContact(eq(EMAIL), any());
        sendInBlueMailingListService.updateNewsOptIn(user, true);
        verify(contactsApi, times(2)).updateContact(eq(EMAIL), any());
        verify(contactsApi).createContact(argThat(a -> a.getEmail().equals(EMAIL) && !a.getListIds().isEmpty()));
        verifyNoMoreInteractions(contactsApi);
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @Transactional
    void addContactToList_ignore_conflict() {

        doThrow(new ApiException(404, "ko")).when(contactsApi).updateContact(eq(EMAIL), any());
        doThrow(new ApiException(409, "ko")).when(contactsApi).createContact(any());
        sendInBlueMailingListService.updateNewsOptIn(createUser(), true);
        verify(contactsApi).updateContact(eq(EMAIL), any());
        verify(contactsApi).createContact(argThat(a -> a.getEmail().equals(EMAIL) && !a.getListIds().isEmpty()));
        verifyNoMoreInteractions(contactsApi);
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    void sendMailSendNotificationsOK() {
        var user = createUser();
        var notif = "notif";
        var templateId = 42L;
        var forcedEmail = "<EMAIL>";
        var sender = "<EMAIL>";
        mockGetUserRepresentationByEmail(user.userId());
        when(transactionalEmailsApi.getSmtpTemplate(templateId)).thenReturn(new GetSmtpTemplateOverview().sender(new GetSmtpTemplateOverviewSender().email(sender)));
        when(transactionalEmailsApi.sendTransacEmail(any())).thenReturn(new CreateSmtpEmail().messageId("id"));
        sendInBlueMailingListService.sendMailsToProfilesForTemplate(Set.of(user), notif, templateId, null, null, forcedEmail);
        verify(notifier).sendMessage(argThat(a -> a.getText().contains(":ok:")));
        verify(transactionalEmailsApi).sendTransacEmail(any());
    }

    @SneakyThrows
    @ParameterizedTest
    @ValueSource(ints = {404, 400})
    @ResetDataAfter
    void sendMailSendNotificationsKO(int statusCode) {
        var user = createUser();
        var notif = "notif";
        var templateId = 42L;
        var forcedEmail = "<EMAIL>";
        var sender = "<EMAIL>";
        mockGetUserRepresentationByEmail(user.userId());
        when(transactionalEmailsApi.getSmtpTemplate(templateId)).thenReturn(new GetSmtpTemplateOverview().sender(new GetSmtpTemplateOverviewSender().email(sender)));
        when(transactionalEmailsApi.sendTransacEmail(any(SendSmtpEmail.class))).thenThrow(new ApiException(statusCode, null));
        sendInBlueMailingListService.sendMailsToProfilesForTemplate(Set.of(user), notif, templateId, null, null, forcedEmail);
        verify(notifier).sendMessage(argThat(a -> a.getText().contains(":warning:")));
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    void sendMailRetry() {
        var user = createUser();
        var notif = "notif";
        var templateId = 42L;
        var forcedEmail = "<EMAIL>";
        var sender = "<EMAIL>";
        when(transactionalEmailsApi.getSmtpTemplate(templateId)).thenReturn(new GetSmtpTemplateOverview().sender(new GetSmtpTemplateOverviewSender().email(sender)));
        when(transactionalEmailsApi.sendTransacEmail(any(SendSmtpEmail.class))).thenThrow(new ApiException(500, null));
        mockGetUserRepresentationByEmail(user.userId());
        GenericTechnicalException caught = null;
        try {
            sendInBlueMailingListService.sendMailsToProfilesForTemplate(Set.of(user), notif, templateId, null, null, forcedEmail);
        } catch (GenericTechnicalException e) {
            caught = e;
        }
        assertNotNull(caught);
    }

    private void mockGetUserRepresentationByEmail(String userId) {
        var userRepresentation = new UserRepresentation();
        userRepresentation.setId(userId);
        when(keycloakMockService.getFOUserRepresentationByEmail(any())).thenReturn(userRepresentation);
    }

    private void mockGetUserRepresentationByUserId(String userId, String email) {
        var userRepresentation = new UserRepresentation();
        userRepresentation.setId(userId);
        userRepresentation.setEmail(email);
        when(keycloakMockService.getFrontOfficeUserProfile(any())).thenReturn(Optional.of(userRepresentation));
    }


    @SneakyThrows
    @Test
    @ResetDataAfter
    void splitBatchSendWhenNotTooManyUsers() {
        ReflectionTestUtils.setField(sendInBlueMailingListService, "maxRecipientsPerRequest", 100);
        var emails = IntStream.range(0, 8).mapToObj("<EMAIL>"::formatted).collect(Collectors.toSet());
        var templateId = 42L;
        var forced = "<EMAIL>";
        var user = createUser();
        mockGetUserRepresentationByEmail(user.userId());
        var templateOverview = new GetSmtpTemplateOverview().sender(new GetSmtpTemplateOverviewSender().email("<EMAIL>"));
        when(transactionalEmailsApi.getSmtpTemplate(any())).thenReturn(templateOverview);

        when(transactionalEmailsApi.sendTransacEmail(any())).thenReturn(mock());
        sendInBlueMailingListService.sendMailsForTemplate(emails, templateId, null, null, forced);
        verify(transactionalEmailsApi).sendTransacEmail(argThat(
                a -> a.getMessageVersions().size() == 9 &&
                     a.getMessageVersions().stream().allMatch(m ->
                             m.getTo().size() == 1
                             && m.getBcc() == null
                             && (emails.contains(m.getTo().get(0).getEmail()) || forced.equals(m.getTo().get(0).getEmail()))
                     )
                     && emails.stream().allMatch(e -> a.getMessageVersions().stream().anyMatch(m -> m.getTo().get(0).getEmail().equals(e)))
                     && a.getMessageVersions().stream().anyMatch(m -> m.getTo().get(0).getEmail().equals(forced))
        ));
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    void splitBatchSendWhenTooManyUsers() {
        ReflectionTestUtils.setField(sendInBlueMailingListService, "maxRecipientsPerRequest", 10);
        var emails = IntStream.range(0, 11).mapToObj("<EMAIL>"::formatted).collect(Collectors.toSet());
        // Total number of recipients : 12 = 11 users to notify + 1 forced recipient
        var templateId = 42L;
        var forced = "<EMAIL>";
        var user = createUser();

        mockGetUserRepresentationByEmail(user.userId());
        var templateOverview = new GetSmtpTemplateOverview().sender(new GetSmtpTemplateOverviewSender().email("<EMAIL>"));
        when(transactionalEmailsApi.getSmtpTemplate(any())).thenReturn(templateOverview);
        when(transactionalEmailsApi.sendTransacEmail(any())).thenReturn(mock());

        sendInBlueMailingListService.sendMailsForTemplate(emails, templateId, null, null, forced);
        verify(transactionalEmailsApi).sendTransacEmail(argThat(
                a -> a.getMessageVersions().size() == 10 &&
                     a.getMessageVersions().stream().allMatch(
                             e -> emails.contains(e.getTo().get(0).getEmail())
                                  && e.getTo().size() == 1
                                  && e.getBcc() == null
                     )));

        verify(transactionalEmailsApi).sendTransacEmail(argThat(
                a -> a.getMessageVersions().size() == 2 &&
                        a.getMessageVersions().stream().anyMatch(m -> m.getTo().get(0).getEmail().equals(forced))
                        && a.getMessageVersions().stream().allMatch(m -> m.getBcc() == null
                        )));
    }

    @Test
    @ResetDataAfter
    void removeUserBlacklist() {
        var user = createUser("<EMAIL>");
        updateUserJobOfferOptOut(user, false);
        sendInBlueMailingListService.updateJobOffersOptIn(user);
        txHelper.doInTransaction(() -> {
            Assertions.assertThat(applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow().getSendersOptOut()).isEmpty();
        });
    }

    @Test
    @ResetDataAfter
    void addUserBlacklist() {
        var user = createUser();
        updateUserJobOfferOptOut(user, true);
        sendInBlueMailingListService.updateJobOffersOptIn(user);
        txHelper.doInTransaction(() -> {
            Assertions.assertThat(applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow().getSendersOptOut()).containsExactly("<EMAIL>");
        });
    }

    private void updateUserJobOfferOptOut(UserProfile userProfile, boolean optOut) {
        userProfile.setJobOfferOptOut(optOut);
        applicationContext.getBean(UserProfileRepository.class).save(userProfile);
    }
}
