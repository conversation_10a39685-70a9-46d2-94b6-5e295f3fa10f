package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.Duration;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.UserRegistrationState;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dto.TransactionalBlackListResult;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import sendinblue.ApiException;
import sibApi.ContactsApi;
import sibApi.SendersApi;
import sibApi.TransactionalEmailsApi;
import sibModel.*;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@EnableRetry
@TestPropertySource(properties = {"sendinblue.apiKey=42", "sendinblue.retry-delay=50"})
class SendInBlueMailingListRetryServiceTest extends AbstractIntegrationTest {

    public static final String EMAIL = "<EMAIL>";
    public static final String OPT_OUT_MAIL = "<EMAIL>";
    public static String USER_ID;
    public static UUID USER_UUID;
    public static UserProfile USER_PROFILE;
    private static final Long JOB_DATING_OPT_IN_ID = 63L;
    private static final String departmentCode = "69";
    private static final String postcode = "69000";
    private static final String regionName = "Auvergne-Rhône-Alpes";

    private final String jobOfferSender = "<EMAIL>";

    @MockBean
    SendInBlueClientConfiguration sendInBlueClientConfiguration;

    @SpyBean
    SendInBlueMailingListService sendInBlueMailingListService;

    @Mock
    ContactsApi contactsApi;

    @Mock
    SendersApi sendersApi;

    @Mock
    TransactionalEmailsApi transactionalEmailsApi;

    GetSendersListSenders sender;
    GetSendersList getSendersList;

    @Autowired
    KeycloakMockService keycloakMockService;

    @Autowired
    private ApplicationContext applicationContext;


    @BeforeEach
    void prepareClient() throws ApiException {
        Mockito.when(sendInBlueClientConfiguration.getContactApi()).thenReturn(contactsApi);
        Mockito.when(sendInBlueClientConfiguration.getSendersApi()).thenReturn(sendersApi);
        Mockito.when(sendInBlueClientConfiguration.getTransactionalEmailApi()).thenReturn(transactionalEmailsApi);
        Mockito.when(sendInBlueClientConfiguration.getJobDatingOptinFrontUsersListId()).thenReturn(JOB_DATING_OPT_IN_ID);
        sender = new GetSendersListSenders().id(1L).email("<EMAIL>").active(true);
        getSendersList = new GetSendersList().senders(List.of(sender));
        Mockito.when(sendersApi.getSenders(null, null)).thenReturn(getSendersList);
        if (USER_ID == null) {
            USER_ID = keycloakMockService.createUserInFrontOfficeRealm(new UserKeycloakRepresentation().setEmail(EMAIL));
            USER_PROFILE = applicationContext.getBean(UserProfileMotherObject.class)
                    .withOptOutMail(OPT_OUT_MAIL)
                    .withUserId(USER_ID)
                    .withRegistrationStep(UserRegistrationState.RegistrationStep.BO_CONFIRMED)
                    .withLocation(Location.builder().departmentCode(departmentCode).postcode(postcode).regionName(regionName).build())
                    .buildAndPersist();
            USER_UUID = USER_PROFILE.uuid();
        }
        createUserFetchedByMailIfNotExists();
    }

    private void createUserFetchedByMailIfNotExists() {
        applicationContext.getBean(UserProfileRepository.class).findByUserId(KeycloakMockService.MOCK_ID_FOR_USER_FETCHED_BY_MAIL).orElseGet(
                () -> applicationContext.getBean(UserProfileMotherObject.class).withUserId(KeycloakMockService.MOCK_ID_FOR_USER_FETCHED_BY_MAIL).buildAndPersist()
        );
    }

    @Test
    void createContact_success() throws ApiException {
        var createUpdateContactModel = new CreateUpdateContactModel().id(1L);
        var email = "<EMAIL>";
        var attributes = new HashMap<String, String>();
        attributes.put("DEPARTEMENT", "");
        attributes.put("REGION", "");
        attributes.put("CODE_POSTAL", "");
        attributes.put("NOM", "");
        attributes.put("PRENOM", "");
        attributes.put("DATE_CREATION", "2020-02-02");
        attributes.put("DATE_LAST_CX", "2020-01-01");
        attributes.put("DATE_LAST_APPLY", "2020-03-03");
        attributes.put("NB_XP", "1");
        attributes.put("SALARY", "20000");
        attributes.put("USER_MOBILE_USAGE", "2");


        var createContact = new CreateContact()
                .email(email)
                .updateEnabled(true)
                .attributes(attributes)
                .smtpBlacklistSender(new ArrayList<>())
                .listIds(List.of(346L));
        Mockito.when(contactsApi.createContact(createContact)).thenReturn(createUpdateContactModel);

        var localDateTime = LocalDateTime.of(2020, 1, 1, 0, 0);
        var date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        var userId = keycloakMockService.createUserInFrontOfficeRealm(new UserKeycloakRepresentation().setEmail(email));

        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withCreationDate(date)
                .withUserId(userId)
                .withLastConnectionDate(localDateTime)
                .withExperience(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist(), Duration.DURATION_4)
                .withSalary(20000)
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                .withSubmissionDate(OffsetDateTime.of(2020, 3, 3, 0, 0, 0, 0, ZoneOffset.UTC))
                .buildAndPersist();

        sendInBlueMailingListService.createContact(userProfile);
        verify(contactsApi).createContact(createContact);
        verifyNoMoreInteractions(contactsApi);
    }

    private void updateUserJobOfferOptOut(boolean optOut) {
        USER_PROFILE.setJobOfferOptOut(optOut);
        applicationContext.getBean(UserProfileRepository.class).save(USER_PROFILE);
    }

    @Test
    void updateOptIn_should_remove_ids() throws ApiException {
        updateUserJobOfferOptOut(true);
        sendInBlueMailingListService.updateJobOffersOptIn(USER_PROFILE);
        verify(contactsApi).updateContact(eq(EMAIL), argThat(c -> c.getSmtpBlacklistSender().contains(jobOfferSender)));
        verifyNoMoreInteractions(contactsApi);
    }

    @Test
    void updateOptIn_should_get_retried() throws ApiException {

        doThrow(new ApiException()).doNothing().when(contactsApi).updateContact(eq(EMAIL), argThat(c -> c.getSmtpBlacklistSender().contains(OPT_OUT_MAIL)));
        updateUserJobOfferOptOut(false);
        sendInBlueMailingListService.updateJobOffersOptIn(USER_PROFILE);
        // Wait for retry to run
        verify(contactsApi, Mockito.timeout(1).times(2))
                .updateContact(eq(EMAIL), argThat(c -> c.getSmtpBlacklistSender().size() == 1 && c.getSmtpBlacklistSender().contains(OPT_OUT_MAIL)));
    }

    @Test
    void sendMailForTemplate_should_use_params() throws ApiException {
        var templateId = 5454L;
        var param = Map.of("a", "b");
        Mockito.when(transactionalEmailsApi.sendTransacEmail(Mockito.any(SendSmtpEmail.class))).thenReturn(Mockito.mock(CreateSmtpEmail.class));
        var templateOverview = new GetSmtpTemplateOverview().sender(new GetSmtpTemplateOverviewSender().email("<EMAIL>"));
        Mockito.when(transactionalEmailsApi.getSmtpTemplate(any())).thenReturn(templateOverview);
        sendInBlueMailingListService.sendMailsForTemplate(Set.of(EMAIL), templateId, param, null);
        verify(transactionalEmailsApi, Mockito.times(1))
                .sendTransacEmail(argThat(e -> e.getMessageVersions().get(0).getTo().get(0).getEmail().equals(EMAIL) && e.getTemplateId().equals(templateId) && e.getParams() == null && e.getMessageVersions().get(0).getParams().equals(param)));
    }

    @Test
    void sendMailForTemplate_should_get_retried() throws ApiException {
        var templateId = 5454L;
        Mockito.when(transactionalEmailsApi.sendTransacEmail(Mockito.any(SendSmtpEmail.class))).thenThrow(new ApiException()).thenReturn(Mockito.mock(CreateSmtpEmail.class));
        var templateOverview = new GetSmtpTemplateOverview().sender(new GetSmtpTemplateOverviewSender().email("<EMAIL>"));
        Mockito.when(transactionalEmailsApi.getSmtpTemplate(any())).thenReturn(templateOverview);
        sendInBlueMailingListService.sendMailsForTemplate(Set.of(EMAIL), templateId, null, null);
        verify(transactionalEmailsApi, Mockito.timeout(500).times(2))
                .sendTransacEmail(argThat(e -> e.getMessageVersions().get(0).getTo().get(0).getEmail().equals(EMAIL) && e.getTemplateId().equals(templateId)));
    }

    @Test
    void sendMail_should_get_retried() throws ApiException {
        var subject = "test subject";
        var expectedSubject = "[Test] (%s)".formatted(subject);
        var recipientName = "JP 2";
        Mockito.when(transactionalEmailsApi.sendTransacEmail(Mockito.any(SendSmtpEmail.class))).thenThrow(new ApiException()).thenReturn(Mockito.mock(CreateSmtpEmail.class));
        Mockito.when(transactionalEmailsApi.getSmtpTemplate(any())).thenReturn(new GetSmtpTemplateOverview().subject(subject));
        sendInBlueMailingListService.sendMail(EMAIL, recipientName, subject, "test", "<EMAIL>", "Iris");
        verify(transactionalEmailsApi, Mockito.timeout(500).times(2))
                .sendTransacEmail(argThat(e -> e.getTo().get(0).getEmail().equals(EMAIL) && e.getSubject().equals(expectedSubject)));
    }

    @Test
    void getTransactionalBlacklistedEmails() throws ApiException {
        var limit = 12L;
        var offset = 42L;
        var email = "<EMAIL>";
        var since = LocalDateTime.now();
        Mockito.when(transactionalEmailsApi.getTransacBlockedContacts(eq(DateTimeFormatter.ofPattern(SendInBlueMailingListService.SIB_SIMPLE_DATE_FORMAT).format(since)), anyString(), eq(limit), eq(offset),
                        isNull(), isNull()))
                .thenReturn(new GetTransacBlockedContacts()
                        .contacts(List.of(new GetTransacBlockedContactsContacts().email(email))));
        var emails = sendInBlueMailingListService.getTransactionalBlacklistedEmails(limit, offset, since);
        assertThat(emails).extracting(TransactionalBlackListResult::getEmail).containsExactly(email);
    }

    @Test
    void getTransactionalBlacklistedEmailsHandleExceptions() throws ApiException {
        var limit = 12L;
        var offset = 42L;
        var since = LocalDateTime.now();
        Mockito.when(transactionalEmailsApi.getTransacBlockedContacts(eq(DateTimeFormatter.ofPattern(SendInBlueMailingListService.SIB_SIMPLE_DATE_FORMAT).format(since)), anyString(), eq(limit), eq(offset),
                        isNull(), isNull()))
                .thenThrow(new ApiException());
        Exception caught = null;
        try {
            sendInBlueMailingListService.getTransactionalBlacklistedEmails(limit, offset, since);
        } catch (GenericTechnicalException e) {
            caught = e;
        }
        assertThat(caught).isNotNull();
    }

    @Test
    void updateLocation_should_update_department_and_region() throws ApiException {
        sendInBlueMailingListService.updateLocation(USER_PROFILE);
        verify(contactsApi).updateContact(eq(EMAIL), argThat(c -> updateContactContainsDepartmentAndRegionAttributes(c, departmentCode, regionName, postcode)));
        verifyNoMoreInteractions(contactsApi);
    }

    @Test
    void updateLocation_is_tolerant_to_null_values() throws ApiException {
        txHelper.doInTransaction(() -> {
            USER_PROFILE =
                    applicationContext.getBean(UserProfileRepository.class)
                            .findById(USER_UUID).orElseThrow();
            USER_PROFILE.generalInformation().setLocation(null);
        });
        sendInBlueMailingListService.updateLocation(USER_PROFILE);
        verify(contactsApi).updateContact(eq(EMAIL), argThat(c -> updateContactContainsDepartmentAndRegionAttributes(c, "", "", "")));
        verifyNoMoreInteractions(contactsApi);
    }

    private boolean updateContactContainsDepartmentAndRegionAttributes(UpdateContact uc, String departmentCode, String regionName, String postcode) {
        var props = ((Properties) uc.getAttributes());
        return departmentCode.equals(props.get("DEPARTEMENT")) &&
               regionName.equals(props.get("REGION")) &&
               postcode.equals(props.get("CODE_POSTAL"));
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void blacklist_from_all_senders() throws ApiException {
        var sender1 = "<EMAIL>";
        var sender2 = "<EMAIL>";
        var senders = List.of(
                new GetSendersListSenders().email(sender1),
                new GetSendersListSenders().email(sender2)
        );
        var responseFromSendersApi = new GetSendersList().senders(senders);
        var expectedBlacklistedSenders = new UpdateContact().smtpBlacklistSender(List.of(sender1, sender2));
        Mockito.when(sendersApi.getSenders(null, null)).thenReturn(responseFromSendersApi);

        sendInBlueMailingListService.blacklistFromAllSenders(USER_PROFILE);

        Mockito.verify(contactsApi).updateContact(anyString(), eq(expectedBlacklistedSenders));
    }
}
