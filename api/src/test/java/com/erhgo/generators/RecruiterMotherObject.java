package com.erhgo.generators;

import com.erhgo.domain.enums.DiffusionType;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.repositories.AbstractOrganizationRepository;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;


@Service
@Scope("prototype")
@NoArgsConstructor
public class RecruiterMotherObject {

    @Autowired
    AbstractOrganizationRepository organizationRepository;
    Recruiter organization = Recruiter.recruiterBuilder().build();

    @Transactional
    public Recruiter buildAndPersist() {
        var orga = organizationRepository.save(build());
        if (orga.getCode() == null) {
            orga.updateCodeOnEntityCreate();
        }
        return orga;
    }

    public Recruiter build() {
        if (organization.getTitle() == null) {
            organization.setTitle("Default title");
        }
        if (organization.getOrganizationType() == null) {
            withOrganizationType(AbstractOrganization.OrganizationType.TERRITORIAL);
        }
        return organization;
    }

    public RecruiterMotherObject withSiret(String siret) {
        organization.setSiret(siret);
        return this;
    }

    public RecruiterMotherObject withOrganizationType(AbstractOrganization.OrganizationType organizationType) {
        ReflectionTestUtils.setField(organization, "organizationType", organizationType);
        return this;
    }

    public RecruiterMotherObject withCode(String organizationCode) {
        ReflectionTestUtils.setField(organization, "code", organizationCode);
        return this;
    }

    public RecruiterMotherObject withWrongSiret(boolean wrongSiret) {
        organization.setSiretVerificationStatus(wrongSiret ? AbstractOrganization.SiretVerificationStatus.WRONG_SIRET : AbstractOrganization.SiretVerificationStatus.SUCCESS);
        return this;
    }

    public RecruiterMotherObject withTitle(String title) {
        organization.setTitle(title);
        return this;
    }

    public RecruiterMotherObject withGdprMention(String gdprMention) {
        organization.setGdprMention(gdprMention);
        return this;
    }

    public RecruiterMotherObject withExternalUrl(String url) {
        organization.setExternalUrl(url);
        return this;
    }

    public RecruiterMotherObject withDescription(String description) {
        organization.setDescription(description);
        return this;
    }

    public RecruiterMotherObject withDefaultDiffusionType(DiffusionType diffusionType) {
        organization.setDefaultDiffusionType(diffusionType);
        return this;
    }
}
