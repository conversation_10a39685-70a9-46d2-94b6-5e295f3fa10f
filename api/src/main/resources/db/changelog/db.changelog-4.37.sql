-- liquibase formatted sql

-- changeset eric:20250611:0
CREATE TABLE `Notification_TMP`
(
    `recruitment_id`            bigint(20)            DEFAULT NULL,
    `userProfile_uuid`          binary(16)   NOT NULL,
    `createdBy_keycloakId`      varchar(255)          DEFAULT NULL,
    `createdDate`               datetime              DEFAULT NULL,
    `lastModifiedBy_keycloakId` varchar(255)          DEFAULT NULL,
    `updatedDate`               datetime              DEFAULT NULL,
    `type`                      varchar(255) NOT NULL,
    `id`                        binary(16)   NOT NULL,
    `DTYPE`                     varchar(31)  NOT NULL,
    `state`                     varchar(255) NOT NULL,
    `content`                   varchar(1500)         DEFAULT NULL,
    `link`                      varchar(1000)         DEFAULT NULL,
    `subject`                   varchar(500)          DEFAULT NULL,
    `requiresMailSending`       bit(1)       NOT NULL DEFAULT b'0',
    PRIMARY <PERSON>EY (`id`),
    KEY `NotifiedRecruitment_User_FOREIGN_KEY_2` (`userProfile_uuid`),
    KEY `NotifiedRecruitment_Recruitment_FOREIGN_KEY_2` (`recruitment_id`),
    KEY `IDX_NOTIFICATION_TYPE_AND_RECRUITMENT_2_2` (`DTYPE`, `recruitment_id`),
    KEY `IDX_NOTIF_TYPE_AND_USER_AND_RECRUITMENT_2` (`DTYPE`, `userProfile_uuid`, `recruitment_id`),
    KEY `IDX_NOTIFICATION_TYPE_2` (`DTYPE`),
    KEY `IDX_NOTIF_TYPE_AND_RECRUITMENT_2` (`DTYPE`, `recruitment_id`),
    CONSTRAINT `NotifiedRecruitment_Recruitment_FOREIGN_KEY_2` FOREIGN KEY (`recruitment_id`) REFERENCES `Recruitment` (`id`),
    CONSTRAINT `NotifiedRecruitment_User_FOREIGN_KEY_2` FOREIGN KEY (`userProfile_uuid`) REFERENCES `UserProfile` (`uuid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

-- changeset eric:20250611:1
insert into Notification_TMP
select *
from Notification
where createdDate >= DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- changeset eric:20250611:2
DROP TABLE Notification;

-- changeset eric:20250611:3
RENAME TABLE Notification_TMP TO Notification;
-- changeset amir:20250610:0
INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
values ('ats.limit.global.FIRECRAWL--STEF', '20');
-- changeset eric:20250520:1
ALTER TABLE Organization
    ADD COLUMN defaultDiffusionType VARCHAR(255) DEFAULT 'BOTH' NULL;
-- changeset eric:20250520:2
UPDATE Organization
SET defaultDiffusionType = 'CV'
WHERE code in (select code
               from ExternalOffer
               where atsCode = 'TEAMTAILOR'
                 AND code <> 'S-21880');
