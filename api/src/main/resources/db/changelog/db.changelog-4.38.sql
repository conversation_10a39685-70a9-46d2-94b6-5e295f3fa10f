-- liquibase formatted sql

-- changeset system:20250623-add-notified-on-closed-recruitment-flag:0
ALTER TABLE RecruitmentCandidature
    ADD COLUMN notifiedOnClosedRecruitment BOOLEAN DEFAULT FALSE;

-- changeset system:20250623-add-notified-on-closed-recruitment-flag:1
UPDATE RecruitmentCandidature rc
    INNER JOIN Recruitment r ON rc.recruitment_id = r.id
SET rc.notifiedOnClosedRecruitment = TRUE
WHERE r.state != 1;
-- changeset eric:20250525:1
ALTER TABLE Recruitment
    ADD COLUMN diffusionType VARCHAR(255);
