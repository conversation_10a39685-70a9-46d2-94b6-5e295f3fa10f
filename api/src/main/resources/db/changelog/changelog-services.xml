<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">
    <changeSet id="1588676879007" author="eric" context="default or e2e">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.fixtures.Fixtures"/>
    </changeSet>
    <changeSet id="1588676879005" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.UpdateKeycloakDomainsAndTypeService"/>
    </changeSet>
    <changeSet id="1595923865000-1" author="morgan">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.ComputeErhgoOccupationBehaviorsCategories"/>
    </changeSet>
    <changeSet id="1603271455000-2" author="eric" context="!default and !test and !e2e">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.ImportErhgoOccupationsCategories"/>
    </changeSet>
    <changeSet id="1605779348000-1" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.ComputeJobsLevel"/>
    </changeSet>
    <changeSet id="1616605135" author="maxime">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.CreateFOGroupAndRoleForEachEnterprise"/>
    </changeSet>
    <changeSet id="1593582573003-1" author="eric" context="!default and !test and !e2e">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.UpdateKeycloakSMTP"/>
    </changeSet>
    <changeSet id="1619448236" author="maxime">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.UpdateErhgoOccupationQualificationState"/>
    </changeSet>
    <changeSet id="1622217148000-4" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.IndexOccupationsLauncher"/>
    </changeSet>
    <changeSet id="1626704967" author="maxime">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.UpdateRegistrationFlow"/>
    </changeSet>
    <changeSet id="1633348472" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.EnableEditUsername"/>
    </changeSet>
    <changeSet id="1636731325-1" author="keltoum">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.AdjustErhgoOccupationLauncher"/>
    </changeSet>
    <changeSet id="1669031845-79" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.IndexUsers"/>
    </changeSet>
    <changeSet id="1658489836-5" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.CreateKeycloakSourcingRealm"/>
    </changeSet>
    <changeSet id="1674739576-4" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.AdjustChannelForSourcingCandidatures"/>
    </changeSet>
    <changeSet id="**********-1" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.CleanupSenderOptOut"/>
    </changeSet>
    <changeSet id="**********-1" author="marin">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.CreateGoogleIdentityProvider"/>
    </changeSet>
    <changeSet id="**********-1" author="marin">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.CreateAppleIdentityProvider"/>
    </changeSet>
    <changeSet id="**********-1" author="clement">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.MasteryLevelUpdater"/>
    </changeSet>
    <changeSet id="**********-11" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.IndexRecruitments"/>
    </changeSet>
    <changeSet id="20240723-3" author="amir">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.RemoveActivitiesFromAlgolia"/>
    </changeSet>
    <changeSet id="04112024-0" author="eric">
        <validCheckSum>ANY</validCheckSum>
        <customChange class="com.erhgo.migrations.changes.UpgradeKeycloak26"/>
    </changeSet>
</databaseChangeLog>
