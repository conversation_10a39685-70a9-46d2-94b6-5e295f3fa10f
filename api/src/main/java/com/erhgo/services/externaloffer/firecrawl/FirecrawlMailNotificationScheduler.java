package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.candidature.AbstractATSMailNotificationSender;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.reminder.EmailToNotifyFilterInterface;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@ConditionalOnProperty(name = "ats.firecrawl.enabled", havingValue = "true")
public class FirecrawlMailNotificationScheduler extends AbstractATSMailNotificationSender {

    public FirecrawlMailNotificationScheduler(RecruitmentCandidatureRepository recruitmentCandidatureRepository,
                                              SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                                              AbstractCandidatureRepository abstractCandidatureRepository,
                                              KeycloakService keycloakService,
                                              ConfigurablePropertyRepository configurablePropertyRepository,
                                              UserProfileCompetencesExportService userProfileCompetencesExportService,
                                              @Nullable MailNotifier mailNotifier,
                                              SecurityService securityService,
                                              List<AtsSendCandidaturesConfig> firecrawlSendCandidaturesConfig,
                                              TransactionTemplate transactionTemplate,
                                              EmailToNotifyFilterInterface emailToNotifyFilterInterface,
                                              SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider
    ) {
        super(recruitmentCandidatureRepository, spontaneousCandidatureRepository, abstractCandidatureRepository, keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, mailNotifier, securityService, firecrawlSendCandidaturesConfig, transactionTemplate, emailToNotifyFilterInterface, sendCandidaturesRecruiterProvider);
    }

    @PostConstruct
    public void init() {
        log.info("FirecrawlMailNotificationHandler initialized");
    }

    @Scheduled(initialDelay = 30, fixedRate = 60, timeUnit = TimeUnit.MINUTES)
    @SchedulerLock(name = "handleNewCandidaturesForFirecrawl", lockAtLeastFor = "35M")
    @Override
    public void handleNewCandidatures() {
        super.handleNewCandidatures();
    }

    @Override
    protected boolean useReplyToAsFrom() {
        return true;
    }

}
