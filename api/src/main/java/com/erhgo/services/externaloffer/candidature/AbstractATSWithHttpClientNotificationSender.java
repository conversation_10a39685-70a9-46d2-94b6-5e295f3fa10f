package com.erhgo.services.externaloffer.candidature;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.HttpStatus;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Slf4j
public abstract class AbstractATSWithHttpClientNotificationSender extends AbstractATSNotificationSender implements ApplicationContextAware {
    protected static final String BEARER_PREFIX = "Bearer ";
    protected RetryableHttpClient httpClient;

    protected AbstractATSWithHttpClientNotificationSender(KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, TransactionTemplate transactionTemplate, RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository, List<AtsSendCandidaturesConfig> candidaturesConfigs, SecurityService securityService, AbstractCandidatureRepository abstractCandidatureRepository, SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, candidaturesConfigs, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.httpClient = applicationContext.getBean(RetryableHttpClient.class, 30, 30, 30);
        log.info("AbstractATSWithHttpClientNotificationSender initialized for {}", getClass().getSimpleName());
    }


    @Override
    protected final void sendCandidature(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) {
        try {
            log.trace("About to send candidature {} to {} URL {}", candidature.getId(), config.getAtsCode(), config.getCandidatureNotificationUrl());
            var request = buildSendCandidatureRequest(userRepresentation, candidature, config);
            try (var response = httpClient.executeRequest(request)) {
                var body = getResponseBodySafely(response, config);
                if (handleAtsResponseIsErroneous(response.code(), body, candidature, config)) {
                    log.error("Unable to send candidature {} to {} URL {}, status: {}, body: {}", candidature.getId(), config.getAtsCode(), config.getCandidatureNotificationUrl(), response.code(), body);
                    throw new GenericTechnicalException("Unable to send candidature %d to %s URL %s".formatted(candidature.getId(), config.getAtsCode(), config.getCandidatureNotificationUrl()));
                }
            }
            log.debug("Candidature {} sent to {}", candidature.getId(), config.getCandidatureNotificationUrl());
        } catch (IOException | IllegalStateException e) {
            log.warn("Unable to send candidature {} to {} URL {}", candidature.getId(), config.getAtsCode(), config.getCandidatureNotificationUrl(), e);
            throw new GenericTechnicalException("Fail to send candidature", e);
        }
    }

    private boolean handleAtsResponseIsErroneous(int code, String body, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) {
        if (shouldConsiderAsAlreadySent(code, body)) {
            log.debug("Ignore already sent candidature {} for config {}", candidature.getId(), config);
            setATSState(candidature, CandidatureSynchronizationState.IGNORE, config);
            return false;
        }
        if (isCandidatureOnClosedRecruitment(code, body)) {
            log.debug("Refuse candidature {} on closed recruitment (remoteId = {}) for config {}", candidature.getId(), candidature.getExternalOfferId(), config);
            refuseCandidature(candidature);
            setATSState(candidature, CandidatureSynchronizationState.IGNORE, config);
            return false;
        }
        return code > 299;
    }

    private void refuseCandidature(RecruitmentCandidature candidature) {
        try {
            transactionTemplate.execute(_na -> {
                abstractCandidatureRepository.findById(candidature.getId()).ifPresentOrElse(c -> {
                    candidature.dismiss("ats");
                    log.debug("Candidature {} refused automaticaly on closed recruitment", c.getId());
                }, () -> log.error("Unable to find candidature {} for ats refusal on closed recruitment", candidature.getId()));
                return null;
            });
        } catch (RuntimeException re) {
            log.error("error on candidature {} refusal for ats", candidature.getId(), re);
        }
    }

    protected boolean isCandidatureOnClosedRecruitment(int code, String body) {
        return false;
    }

    protected boolean shouldConsiderAsAlreadySent(int code, String body) {
        return
                (code == HttpStatus.SC_CONFLICT) ||
                        (code == HttpStatus.SC_GONE) ||
                        (code == HttpStatus.SC_BAD_REQUEST && bodyContainsAlreadyAppliedLabel(body));
    }

    private static boolean bodyContainsAlreadyAppliedLabel(String body) {
        return Optional.ofNullable(body)
                .map(StringUtils::normalizeLowerCase)
                .filter(c -> (c.contains("deja") && c.contains("postule"))
                        || (c.contains("already") && c.contains("applied")))
                .isPresent();
    }

    protected abstract Request buildSendCandidatureRequest(UserRepresentation userRepresentation, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) throws IOException;

    private static @NotNull String getResponseBodySafely(Response response, AtsSendCandidaturesConfig config) {
        return Optional.ofNullable(response.body()).map(a -> {
            try {
                return a.string();
            } catch (IOException | RuntimeException e) {
                log.warn("Unable to extract body on {} send candidature call", config.getAtsCode(), e);
                return null;
            }
        }).orElse("<vide ou inconnu>");
    }

}
