package com.erhgo.services.externaloffer.recruiterdispatcher.fetch;

import com.erhgo.domain.referential.Recruiter;
import com.erhgo.openapi.dto.DiffusionTypeDTO;
import com.erhgo.openapi.dto.OrganizationTypeDTO;
import com.erhgo.openapi.dto.SaveOrganizationCommandDTO;
import com.erhgo.services.OrganizationService;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem;
import com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItemRepository;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.ATSRecruiterCreated;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

/**
 * Create unknown recruiter
 */
@Service
@Slf4j
class PerOfferFetcherCreateUnknownRecruiter extends PerOfferFetcher {

    private final OrganizationService organizationService;
    private final PerOfferATSConfigurationItemRepository perOfferATSConfigurationItemRepository;
    private final Notifier notifier;

    public PerOfferFetcherCreateUnknownRecruiter(PerOfferATSConfigurationItemRepository repository, OrganizationService organizationService, PerOfferATSConfigurationItemRepository perOfferATSConfigurationItemRepository, Notifier notifier) {
        super(repository);
        this.organizationService = organizationService;
        this.perOfferATSConfigurationItemRepository = perOfferATSConfigurationItemRepository;
        this.notifier = notifier;
    }

    @Override
    protected <A extends AbstractRemoteOfferContent<A>> @Nullable String getRecruiterCode(A offer, AtsGetOfferConfig atsConfig, Optional<PerOfferATSConfigurationItem> atsConfiguration, String remoteRecruiterCode) {
        if (atsConfiguration.isEmpty()) {
            if (StringUtils.isBlank(offer.getRemoteRecruiterTitle())) {
                log.error("Unknown offer remote recruiter code {} for offer {} and ats {}", remoteRecruiterCode, offer.getId(), atsConfig);
                return null;
            }
            var recruiter = createRecruiter(offer, atsConfig, remoteRecruiterCode);
            if (recruiter == null) {
                return null;
            }
            return recruiter.getCode();
        }
        return atsConfiguration.get().getRecruiterCode();
    }

    private void persistConfig(AtsGetOfferConfig atsConfig, String remoteRecruiterCode, Recruiter recruiter) {
        perOfferATSConfigurationItemRepository.save(new PerOfferATSConfigurationItem(UUID.randomUUID(), atsConfig.getAtsCode(), "ALL", remoteRecruiterCode, "", recruiter.getCode()));
    }

    private <A extends AbstractRemoteOfferContent<A>> Recruiter createRecruiter(A offer, AtsGetOfferConfig atsConfig, String remoteRecruiterCode) {
        try {
            Recruiter recruiter = organizationService.save(new SaveOrganizationCommandDTO().defaultDiffusionType(DiffusionTypeDTO.CV).title(offer.getRemoteRecruiterTitle()).organizationType(OrganizationTypeDTO.SOURCING));
            log.info("Recruiter {} ({}) created for offer {} and config {}", recruiter.getTitle(), recruiter.getCode(), offer, atsConfig);
            persistConfig(atsConfig, remoteRecruiterCode, recruiter);
            notifier.sendMessage(new ATSRecruiterCreated(recruiter.getTitle(), recruiter.getCode(), atsConfig.getAtsCode(), offer.getRecruitersInfos()));
            return recruiter;
        } catch (RuntimeException e) {
            log.error("unable to create recruiter {} for offer {} and config {}", offer.getRemoteRecruiterTitle(), offer, atsConfig, e);
            return null;
        }
    }

}
