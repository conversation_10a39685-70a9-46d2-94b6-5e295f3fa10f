package com.erhgo.services.externaloffer.candidature;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.recruiterdispatcher.send.SendCandidaturesRecruiterProvider;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.reminder.EmailToNotifyFilterInterface;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.google.common.base.Joiner;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public abstract class AbstractATSMailNotificationSender extends AbstractATSNotificationSender {

    private static final String MAIL_SUBJECT = "jenesuisPASunCV - Nouvelle candidature au poste %s";

    private static final String MAIL_BODY = """
            Bonjour,
            
            %s %s (email: %s) a candidaté à l'offre %s (%s) depuis jenesuisPASunCV.
            
            Vous trouverez en pièces jointes son profil de compétence anonyme et nominatif.
            
            N'hésitez pas à revenir vers nous pour plus d'informations : <EMAIL>,
            
            Cordialement,
            
            L'équipe recrutement de jenesuisPASunCV
            """.replace("\n", "\r\n");

    private final MailNotifier mailNotifier;
    private final EmailToNotifyFilterInterface emailToNotifyFilterInterface;

    protected AbstractATSMailNotificationSender(RecruitmentCandidatureRepository recruitmentCandidatureRepository, SpontaneousCandidatureRepository spontaneousCandidatureRepository,
                                                AbstractCandidatureRepository abstractCandidatureRepository,
                                                KeycloakService keycloakService, ConfigurablePropertyRepository configurablePropertyRepository, UserProfileCompetencesExportService userProfileCompetencesExportService, @Nullable MailNotifier mailNotifier, SecurityService securityService, List<AtsSendCandidaturesConfig> sendCandidaturesConfig, TransactionTemplate transactionTemplate, EmailToNotifyFilterInterface emailToNotifyFilterInterface,
                                                SendCandidaturesRecruiterProvider sendCandidaturesRecruiterProvider
    ) {
        super(keycloakService, configurablePropertyRepository, userProfileCompetencesExportService, transactionTemplate, recruitmentCandidatureRepository, spontaneousCandidatureRepository, sendCandidaturesConfig, securityService, abstractCandidatureRepository, sendCandidaturesRecruiterProvider);
        this.mailNotifier = mailNotifier;
        this.emailToNotifyFilterInterface = emailToNotifyFilterInterface;
    }

    @Override
    protected void sendCandidature(UserRepresentation user, SpontaneousCandidature candidature, AtsSendCandidaturesConfig config) {
        sendCandidatureInternal(user, candidature, config);
    }

    @Override
    protected void sendCandidature(UserRepresentation user, RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) {
        sendCandidatureInternal(user, candidature, config);
    }

    private void sendCandidatureInternal(UserRepresentation user, AbstractCandidature candidature, AtsSendCandidaturesConfig config) {
        var recipients = filterEmails(getRecipients(candidature, config));
        try {
            var anonymousProfile = getProfile(candidature, user.getFullname(), ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS);
            var profile = getProfile(candidature, user.getFullname(), ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE);
            if (mailNotifier != null) {
                mailNotifier.sendMail(recipients,
                        user.getEmail(),
                        user.getFullname(),
                        useReplyToAsFrom(),
                        candidature.isRecruitmentCandidature() ? buildSubject((RecruitmentCandidature) candidature) : buildSubject((SpontaneousCandidature) candidature),
                        candidature.isRecruitmentCandidature() ? buildBody((RecruitmentCandidature) candidature, user) : buildBody((SpontaneousCandidature) candidature, user),
                        profile,
                        anonymousProfile);
                log.debug("mail correctly sent to %s for ATS %s and candidature %d".formatted(recipients, config.getAtsCode(), candidature.getId()));
            } else {
                log.error("No mail notifier configured - ats will NOT be notified for {}", candidature.getId());
                throw new GenericTechnicalException("No mail notifier configured - ats will NOT be notified for candidature %d".formatted(candidature.getId()));
            }
        } catch (IOException e) {
            log.error("Unable to produce file part or send mail for candidature {} to {}", candidature.getId(), recipients, e);
            throw new GenericTechnicalException("Unable to send candidature %d to %s".formatted(candidature.getId(), config.getAtsCode()));
        }
    }

    private Collection<String> filterEmails(Collection<String> recipients) {
        return filterEmailInNonProdEnvironment() ? recipients.stream().filter(emailToNotifyFilterInterface::emailAccepted).collect(Collectors.toSet()) : recipients;
    }

    protected boolean useReplyToAsFrom() {
        return false;
    }

    protected Collection<String> getRecipients(AbstractCandidature candidature, AtsSendCandidaturesConfig config) {
        if (candidature.isRecruitmentCandidature()) {
            return getRecipientsForRecruitmentCandidature((RecruitmentCandidature) candidature, config);
        } else {
            return getRecipientsForSpontaneousCandidature(config);
        }
    }

    protected @NotNull Set<String> getRecipientsForSpontaneousCandidature(AtsSendCandidaturesConfig config) {
        return Set.of(config.getSpontaneousCandidatureNotificationEmail().split(","));
    }

    protected Collection<String> getRecipientsForRecruitmentCandidature(RecruitmentCandidature candidature, AtsSendCandidaturesConfig config) {
        var recipient = Optional.ofNullable((candidature).getRecruitment().getExternalOffer()).map(ExternalOffer::getCandidatureEmail);
        if (StringUtils.isNotBlank(config.getCandidatureNotificationMail())) {
            if(recipient.isPresent())
                log.warn("Force recipient to send candidature {} to {}", candidature, recipient);
            recipient = Optional.of(config.getCandidatureNotificationMail());
        }
        return Set.of(recipient
                .orElseThrow(() -> new GenericTechnicalException("Unable to send candidature %d to %s: no email to send to".formatted(candidature.getId(), config.getAtsCode())))
                .split(",")
        );
    }

    @Override
    String getRemoteNotifiedIdentifier(AbstractCandidature candidature, AtsSendCandidaturesConfig config) {
        return Joiner.on(",").join(getRecipients(candidature, config));
    }

    protected String buildBody(SpontaneousCandidature candidature, UserRepresentation userRepresentation) {
        throw new IllegalStateException("Not implemented");
    }

    protected String buildSubject(SpontaneousCandidature candidature) {
        throw new IllegalStateException("Not implemented");
    }

    protected String buildBody(RecruitmentCandidature candidature, UserRepresentation userRepresentation) {
        return MAIL_BODY.formatted(
                StringUtils.trimToEmpty(userRepresentation.getFirstName()),
                StringUtils.trimToEmpty(userRepresentation.getLastName()),
                userRepresentation.getEmail(),
                candidature.getJobTitle(),
                Optional.ofNullable(candidature.getLocation()).map(Location::getCity).orElse("")
        );
    }

    protected String buildSubject(RecruitmentCandidature candidature) {
        return MAIL_SUBJECT.formatted(candidature.getJobTitle());
    }

    protected boolean filterEmailInNonProdEnvironment() {
        return true;
    }
}
