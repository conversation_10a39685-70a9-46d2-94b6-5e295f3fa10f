package com.erhgo.services.search.recruitment;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.Recruiter;
import org.springframework.scheduling.annotation.Async;

public interface RecruitmentIndexer {

    void index(Recruitment recruitment);

    @Async
    void remove(Recruitment recruitment);

    void indexAll();

    void indexRecruitmentsForRecruiter(Recruiter recruiter);
}
