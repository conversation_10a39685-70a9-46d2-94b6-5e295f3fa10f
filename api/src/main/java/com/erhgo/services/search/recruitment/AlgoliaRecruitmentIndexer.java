package com.erhgo.services.search.recruitment;

import com.algolia.search.DefaultSearchClient;
import com.algolia.search.SearchClient;
import com.algolia.search.SearchIndex;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.enums.DiffusionType;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.services.search.AlgoliaUtils;
import com.erhgo.utils.SalaryUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.erhgo.utils.StringUtils.removeHtmlTag;

@Service
@Slf4j
@ConditionalOnExpression("!'${algolia.applicationId}'.isEmpty() and !'${algolia.adminApiKey}'.isEmpty()")
public class AlgoliaRecruitmentIndexer implements RecruitmentIndexer {

    // See https://www.algolia.com/doc/guides/sending-and-managing-data/prepare-your-data/in-depth/index-and-records-size-and-usage-limitations/#record-size-limits
    private static final int MAX_DESCRIPTION_SIZE = 10_000;
    private final RecruitmentRepository recruitmentRepository;
    private SearchIndex<AlgoliaRecruitmentDTO> offerIndex;

    public AlgoliaRecruitmentIndexer(
            @Value("${algolia.applicationId}") String applicationId,
            @Value("${algolia.adminApiKey}") String apiKey,
            @Value("${algolia.indexPrefix}") String indexPrefix,
            RecruitmentRepository recruitmentRepository
    ) {
        log.info("Initializing Algolia recruitment indexation client with application ID '{}' and position prefix '{}'", applicationId, indexPrefix);
        var searchClient = DefaultSearchClient.create(applicationId, apiKey);

        createRecruitmentIndex(indexPrefix, searchClient);
        this.recruitmentRepository = recruitmentRepository;
    }

    private void createRecruitmentIndex(String indexPrefix, SearchClient client) {
        offerIndex = client.initIndex("%s_offer_index".formatted(indexPrefix), AlgoliaRecruitmentDTO.class);
        offerIndex.setSettings(
                AlgoliaUtils.commonIndexSettings()
                        .setSearchableAttributes(List.of("title", "recruiter,city", "criteria,keywords,contractType,activities,description", "alternativeLabels"))
                        .setAttributesForFaceting(List.of("contractType", "organizationCode", "diffusionType"))
        );
    }

    @Override
    @Async
    @Transactional
    public void index(Recruitment recruitmentIn) {
        recruitmentRepository.findById(recruitmentIn.getId())
                .ifPresent(recruitment ->
                        offerIndex.saveObject(new AlgoliaRecruitmentDTO(recruitment))
                );
    }

    @Override
    @Async
    public void remove(Recruitment recruitment) {
        offerIndex.deleteObject(recruitment.getCode());
        log.info("Recruitment {} removed from Algolia", recruitment.getId());
    }


    @Override
    @Async
    @Transactional
    public void indexAll() {
        var recruitments = recruitmentRepository.findRecruitmentsToIndex();
        var filteredRecruitments = recruitments.stream()
                .filter(recruitment -> recruitment.getEffectiveDiffusionType() != DiffusionType.NONE)
                .toList();

        var all = filteredRecruitments.stream().map(AlgoliaRecruitmentDTO::new).toList();
        log.info("About to index {} recruitments in Algolia (excluded {} with NONE diffusion type)...",
                all.size(), recruitments.size() - filteredRecruitments.size());
        offerIndex.replaceAllObjects(all, true);
        log.info("{} recruitments successfully indexed in Algolia", all.size());
    }

    @Override
    @Async
    @Transactional
    public void indexRecruitmentsForRecruiter(Recruiter recruiter) {
        var recruitments = recruitmentRepository.findRecruitmentToIndexByRecruiter(recruiter);
        if (recruitments.isEmpty()) {
            log.info("No recruitments found for recruiter {}", recruiter.getCode());
            return;
        }
        var recruitmentsByDiffusionType = recruitments.stream()
                .collect(Collectors.groupingBy(Recruitment::getEffectiveDiffusionType));

        var noneTypeRecruitments = recruitmentsByDiffusionType.getOrDefault(DiffusionType.NONE, List.of());
        if (!noneTypeRecruitments.isEmpty()) {
            var recruitmentCodes = noneTypeRecruitments.stream()
                    .map(Recruitment::getCode)
                    .toList();
            offerIndex.deleteObjects(recruitmentCodes);
            log.info("Removed {} recruitments from index for recruiter {} with NONE diffusion type",
                    noneTypeRecruitments.size(), recruiter.getCode());
        }
        var nonNoneTypeRecruitments = recruitments.stream()
                .filter(r -> r.getEffectiveDiffusionType() != DiffusionType.NONE)
                .toList();

        if (!nonNoneTypeRecruitments.isEmpty()) {
            var recruitmentDTOs = nonNoneTypeRecruitments.stream()
                    .map(AlgoliaRecruitmentDTO::new)
                    .toList();
            offerIndex.saveObjects(recruitmentDTOs);
            log.info("Indexed {} recruitments for recruiter {}",
                    nonNoneTypeRecruitments.size(), recruiter.getCode());
        }
    }

    @Getter
    static class AlgoliaRecruitmentDTO {

        private final String objectID;
        private final Long id;
        private final String title;
        private final Set<String> keywords;
        private final Set<String> alternativeLabels;
        private final String contractType;
        private final Set<String> activities;
        private final String description;
        private final String recruiter;
        private final String city;
        private final Integer salaryMin;
        private final Integer salaryMax;
        private final String salaryLabel;
        private final Integer workDuration;
        private final String workDurationLabel;
        private final String organizationCode;
        private final String diffusionType;

        @JsonProperty("_geoloc")
        private final AlgoliaUtils.AlgoliaGeoloc geolocation;
        private final Date publicationDate;
        private final String publicationDateLabel;
        private final String remoteWorkLabel;

        public AlgoliaRecruitmentDTO(Recruitment recruitment) {
            this.objectID = recruitment.getCode();
            this.id = recruitment.getId();
            this.title = recruitment.getJobTitle();
            this.keywords = recruitment.getKeywords();
            this.alternativeLabels = new HashSet<>(recruitment.getOccupationAlternativeLabels());

            this.description = org.apache.commons.lang3.StringUtils.abbreviate(removeHtmlTag(recruitment.getRecruitmentOrOccupationDescription()), MAX_DESCRIPTION_SIZE);
            this.activities = recruitment.getActivitiesLabels();
            this.contractType = Optional.ofNullable(recruitment.getTypeContract()).map(c -> c == ContractType.CP ? ContractType.CA.name() : c.name()).orElse(null);
            this.recruiter = recruitment.getRecruiterTitle();
            this.city = Optional.ofNullable(recruitment.getLocation()).map(Location::getCity).orElse("");
            this.geolocation = AlgoliaUtils.AlgoliaGeoloc.forLocation(recruitment.getLocation());
            this.salaryMin = recruitment.getBaseSalary();
            this.salaryMax = recruitment.getMaxSalary();
            salaryLabel = SalaryUtils.getSalaryLabel(recruitment);
            this.workDuration = recruitment.getWorkingWeeklyTime();
            this.workDurationLabel = workDuration == null ? "" :
                    "%d heures/semaine%s".formatted(workDuration, BooleanUtils.isTrue(recruitment.getModularWorkingTime()) ? " (variable)" : "");
            this.publicationDate = recruitment.getPublicationDate();
            var formatter = new SimpleDateFormat("dd MMMM yyyy", Locale.FRANCE);
            this.publicationDateLabel = Optional.ofNullable(publicationDate).map(formatter::format).orElse("");
            this.remoteWorkLabel = recruitment.getCriteriaValues().stream()
                    .filter(CriteriaValue::isRelatedToRemoteWork)
                    .map(CriteriaValue::getTitleForBO)
                    .findFirst()
                    .orElse("");
            this.organizationCode = recruitment.getRecruiterCode();
            this.diffusionType = Optional.ofNullable(recruitment.getEffectiveDiffusionType())
                    .map(Enum::name)
                    .orElse("BOTH");
        }
    }
}
