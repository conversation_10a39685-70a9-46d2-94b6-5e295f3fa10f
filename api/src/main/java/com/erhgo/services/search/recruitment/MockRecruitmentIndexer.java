package com.erhgo.services.search.recruitment;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.Recruiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ConditionalOnMissingBean(AlgoliaRecruitmentIndexer.class)
public class MockRecruitmentIndexer implements RecruitmentIndexer {


    @Override
    public void index(Recruitment recruitment) {
        log.error("index - Recruitment indexation is mocked - do nothing");
    }

    @Override
    public void remove(Recruitment recruitment) {
        log.error("remove - Recruitment indexation is mocked - do nothing");
    }

    @Override
    public void indexAll() {
        log.error("index all - Recruitment indexation is mocked - do nothing");
    }

    @Override
    public void indexRecruitmentsForR<PERSON>ruiter(Recruiter recruiter) {
        log.error("indexRecruitmentsForRecruiter - Recruitment indexation is mocked - do nothing");
    }
}
