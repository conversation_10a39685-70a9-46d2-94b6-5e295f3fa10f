package com.erhgo.services.recruitmentimporter;

import com.erhgo.domain.exceptions.AbstractFunctionalException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.services.OrganizationService;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dto.CsvCreationResult;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.generation.dto.OccupationForLabelGenerationResult;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.services.notifier.messages.CSVImportEndsMessageDTO;
import com.erhgo.services.notifier.messages.CSVImportStartsMessageDTO;
import com.erhgo.services.search.GeoService;
import com.erhgo.services.sourcing.SourcingJobRecruitmentService;
import com.erhgo.services.sourcing.SourcingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Service
@Slf4j
public class OfferCsvImporterService {

    // Index: starts at 0 but we want logged row num to start at 1. And 1 header line is skipped => first offer is at "line 2" in user's CSV
    public static final int INDEX_TO_ROW_NUM_OFFSET = 2;
    private final OfferCsvParserService parser;
    private final SourcingJobRecruitmentService sourcingJobService;
    private final GeoService geoService;
    private final OccupationForLabelGenerationService erhgoOccupationImporterService;
    private final RecruiterRepository recruiterRepository;
    private final OrganizationService organizationService;
    private final SourcingService sourcingService;
    private final SourcingKeycloakService sourcingKeycloakService;
    private final SecurityService securityService;
    private final KeycloakService keycloakService;
    private final Notifier notifier;

    private final ApplicationContext applicationContext;

    public void importRecruitments(MultipartFile file) {
        log.info("Importing recruitments from file {}", file.getOriginalFilename());
        List<OfferCsvItem> offers = null;
        try {
            offers = parser.parseOffersFromCsv(file.getInputStream(), OfferCsvItem.class);
        } catch (IOException e) {
            throw new GenericTechnicalException("Unable to read file", e);
        }
        log.info("File parsed successfully, {} offers", offers.size());
        applicationContext.getBean(OfferCsvImporterService.class).createRecruitmentsForOffers(keycloakService.getBackOfficeUserProfile(securityService.getAuthenticatedUserId()).map(UserRepresentation::getEmail).orElse("<Inconnu>"), offers);
        log.info("{} recruitments creation running...", offers.size());
    }

    @Async
    CompletableFuture<List<CsvCreationResult>> createRecruitmentsForOffers(String user, List<OfferCsvItem> offers) {
        notifyCsvImportStart(user, offers.size());
        List<CsvCreationResult> result = null;
        Map<String, Recruiter> createdRecruiterPerName = new TreeMap<>(com.erhgo.utils.StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE);
        try {
            result = IntStream.range(INDEX_TO_ROW_NUM_OFFSET, offers.size() + INDEX_TO_ROW_NUM_OFFSET).mapToObj(rowNum -> {
                var offerJob = offers.get(rowNum - INDEX_TO_ROW_NUM_OFFSET);
                try {
                    return processOffer(rowNum, offerJob, createdRecruiterPerName);
                } catch (RuntimeException e) {
                    log.error("failed to create recruitment from csv import for offer title {} and recruiter {}", offerJob.getOfferTitle(), offerJob.getOrganizationCode(), e);
                    return new CsvCreationResult(rowNum, null, offerJob.getOrganizationCode(), StringUtils.isNotBlank(offerJob.getOrganizationName()), "Erreur technique au traitement de la ligne", "");
                }
            }).toList();
        } finally {
            notifyCsvImportEnd(user, result);
        }
        return CompletableFuture.completedFuture(result);
    }

    private void notifyCsvImportEnd(String user, List<CsvCreationResult> result) {
        notifier.sendMessage(new CSVImportEndsMessageDTO(user, result));
    }

    private void notifyCsvImportStart(String user, int size) {
        notifier.sendMessage(new CSVImportStartsMessageDTO(user, size));
    }

    private @NotNull CsvCreationResult processOffer(int rowNum, OfferCsvItem offerJob, Map<String, Recruiter> recruiterPerName) {
        if (StringUtils.isBlank(offerJob.getOfferTitle())) {
            return new CsvCreationResult(rowNum, null, offerJob.getOrganizationCode(), StringUtils.isNotBlank(offerJob.getOrganizationName()), "Aucun titre d'offre pour la ligne %d ; ligne vide ?".formatted(rowNum), "");
        }
        var recruiter = createOrRetrieveRecruiter(offerJob, rowNum, recruiterPerName);
        if (recruiter == null) {
            return new CsvCreationResult(rowNum, null, offerJob.getOrganizationCode(), StringUtils.isNotBlank(offerJob.getOrganizationName()), "Organisation %s inconnue".formatted(offerJob.getOrganizationCode()), "");
        }
        var result = erhgoOccupationImporterService.createOrUpdateOccupation(offerJob.getOfferTitle(), OccupationCreationSourceType.FROM_CSV);
        if (result.inError()) {
            log.error("Probably wrong title, unable to generate occupation for {}", offerJob.getOfferTitle());
            return new CsvCreationResult(rowNum, null, recruiter.getCode(), StringUtils.isNotBlank(offerJob.getOrganizationName()), "Erreur à la création du métier", "");
        } else {
            Long recruitmentId;
            String warningMessage = "";
            try {
                var coordinates = geoService.fetchGeoCoordinates(offerJob.getCity(), "Import CSV - offer %s for %s".formatted(offerJob.getOfferTitle(), recruiter.getCode()));
                warningMessage = createManagerEmailSourcingAccount(offerJob.getManagerEmail(), recruiter, rowNum);
                recruitmentId = createRecruitment(offerJob, result, recruiter, coordinates);
                log.debug("Recruitment {} ({}) created successfully for rowNum {} - let's publish it", recruitmentId, offerJob.getOfferTitle(), rowNum);
            } catch (RuntimeException e) {
                log.error("failed to create recruitment from csv import for offer title {} and recruiter {} (rowNum {})", offerJob.getOfferTitle(), recruiter.getCode(), rowNum, e);
                return new CsvCreationResult(rowNum, null, recruiter.getCode(), StringUtils.isNotBlank(offerJob.getOrganizationName()), "Erreur technique à la création du recrutement", warningMessage);
            }

            try {
                publishRecruitment(recruitmentId);
                log.debug("Recruitment {} published successfully for rowNum {} ({})", recruitmentId, rowNum, offerJob.getOfferTitle());
                return new CsvCreationResult(rowNum, recruitmentId, recruiter.getCode(), StringUtils.isNotBlank(offerJob.getOrganizationName()), "", warningMessage);
            } catch (RuntimeException e) {
                log.error("failed to create recruitment from csv import for offer title {} and recruiter {} (rowNum {})", offerJob.getOfferTitle(), recruiter.getCode(), rowNum, e);
                return new CsvCreationResult(rowNum, recruitmentId, recruiter.getCode(), StringUtils.isNotBlank(offerJob.getOrganizationName()), "Erreur technique à la publication du recrutement", "Attention : poste partiellement créé (id : %d)".formatted(recruitmentId));
            }
        }
    }

    private void publishRecruitment(Long recruitmentId) {
        sourcingJobService.changeRecruitmentStateForBatch(recruitmentId, new ChangeSourcingRecruitmentStateCommandDTO()
                .nextState(RecruitmentStateDTO.PUBLISHED)
                .sendNotifications(UsersToNotifySelectionTypeDTO.NEW));
        log.trace("recruitment with id {} published from CSV", recruitmentId);
    }

    private Long createRecruitment(OfferCsvItem offerJob, OccupationForLabelGenerationResult result, Recruiter recruiter, LocationDTO coordinates) {
        Long recruitmentId;
        recruitmentId = sourcingJobService.createOrUpdateRecruitmentForBatch(
                recruiter.getCode(),
                new CreateOrUpdateFullRecruitmentCommandDTO()
                        .title(offerJob.getOfferTitle())
                        .jobOccupationId(result.uuid())
                        .typeContractCategory(offerJob.getContractType())
                        .jobLocation(new LocationDTO()
                                .city(coordinates.getCity())
                                .longitude(coordinates.getLongitude())
                                .latitude(coordinates.getLatitude())
                                .citycode(coordinates.getCitycode())
                                .postcode(coordinates.getPostcode())
                        )
                        .organizationDescription(offerJob.getOrganizationDescription())
                        .description(offerJob.getOfferDescription())
                        .workingTimeType(offerJob.getWorkingTimeType())
                        .workingWeeklyTime(optionalFloatToInteger(offerJob.getWeeklyWorkingTime()))
                        .workContractDuration(optionalFloatToInteger(offerJob.getContractDurationInMonth()))
                        .workContractDurationUnit(Optional.ofNullable(offerJob.getContractDurationInMonth())
                                .filter(a -> a > 0)
                                .map(a -> WorkContractDurationUnitDTO.MONTH)
                                .orElse(null))
                        .baseSalary(optionalFloatToInteger(offerJob.getMinSalary()))
                        .maxSalary(optionalFloatToInteger(offerJob.getMaxSalary()))
                        .criteriaValues(offerJob.getCriteria())
                        .startingDate(offerJob.getStartingDate())
                        .diffusionType(Optional.ofNullable(offerJob.getDiffusionType()).map(Enum::name).map(DiffusionTypeDTO::valueOf).orElse(null))
                ,
                OccupationCreationSourceType.FROM_CSV.getLabel(),
                trimToEmptyList(offerJob.getManagerEmail()));
        log.trace("recruitment with id {} created from CSV (recruiter: {}, title: {})", recruitmentId, recruiter.getCode(), offerJob.getOfferTitle());
        return recruitmentId;
    }

    private String createManagerEmailSourcingAccount(String email, Recruiter recruiter, int rowNum) {
        try {
            if (com.erhgo.utils.StringUtils.isEmail(email)) {
                if (isSourcingUserCreationRequired(email, recruiter)) {
                    sourcingService.inviteAndCreateNewUser(new InviteAndCreateNewUserCommandDTO()
                            .email(email)
                            .forceEmail(true)
                            .organizationCode(recruiter.getCode())
                            .fullname(" - "));
                    log.debug("Email {} created and invited - rowNum {}, recruiter {}", email, rowNum, recruiter.getCode());
                    return "";
                }
                log.debug("Email {} already exists - rowNum {}, recruiter {}", email, rowNum, recruiter.getCode());
                return "";
            } else if (StringUtils.isNotBlank(email)) {
                log.warn("Ignoring invalid email {} at rowNum {}, recruiter {}", email, rowNum, recruiter.getCode());
                return "L'email %s mentionné ligne %d n'est pas valide - ignoré".formatted(email, rowNum);
            }
            return "";
        } catch (RuntimeException e) {
            log.warn("Unable to create user {} on rowNum {} for recruiter {}", email, rowNum, recruiter.getTitle(), e);
            return "Une erreur technique est survenue : l'email %s mentionné ligne %d est ignoré".formatted(email, rowNum);
        }
    }

    private boolean isSourcingUserCreationRequired(String email, Recruiter recruiter) {
        return sourcingKeycloakService.getSourcingUsersForGroup(recruiter.getCode()).stream().noneMatch(u -> u.getEmail().equalsIgnoreCase(email));
    }

    private Recruiter createOrRetrieveRecruiter(OfferCsvItem offerJob, int rowNum, Map<String, Recruiter> recruiterPerName) {
        if (StringUtils.isNotBlank(offerJob.getOrganizationCode())) {
            if (StringUtils.isNotBlank(offerJob.getOrganizationName())) {
                log.warn("Orga code {} AND orga name {} provided for recruitment at rowNum {} - ignoring name", offerJob.getOrganizationCode(), offerJob.getOrganizationName(), rowNum);
            }
            return recruiterRepository.findOneByCode(offerJob.getOrganizationCode());
        }
        if (StringUtils.isBlank(offerJob.getOrganizationName())) {
            throw new AbstractFunctionalException("Either code or name must be provided, rowNum %d".formatted(rowNum)) {
            };
        }
        return recruiterPerName.computeIfAbsent(offerJob.getOrganizationName(), n -> organizationService.save(new SaveOrganizationCommandDTO().title(n).organizationType(OrganizationTypeDTO.SOURCING)));
    }

    private static @NotNull List<String> trimToEmptyList(String text) {
        return Stream.of(text).map(StringUtils::trimToNull).filter(Objects::nonNull).toList();
    }

    private static @Nullable Integer optionalFloatToInteger(Float intValue) {
        return Optional.ofNullable(intValue).map(Float::intValue).orElse(null);
    }

}
