package com.erhgo.services.recruitmentimporter;

import com.erhgo.domain.enums.DiffusionType;
import com.erhgo.openapi.dto.DiffusionTypeDTO;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.services.recruitmentimporter.converters.*;
import com.opencsv.bean.CsvCustomBindByPosition;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

@Data
public class OfferCsvItem {

    @CsvCustomBindByPosition(position = 0, converter = StringQuoteTrimConverter.class)
    private String organizationCode;

    @CsvCustomBindByPosition(position = 1, converter = StringQuoteTrimConverter.class)
    private String organizationName;

    @CsvCustomBindByPosition(position = 2, converter = StringQuoteTrimConverter.class)
    private String offerTitle;

    @CsvCustomBindByPosition(position = 3, converter = CustomTypeContractCategoryConverter.class)
    private TypeContractCategoryDTO contractType;

    @CsvCustomBindByPosition(position = 4, converter = StringQuoteTrimConverter.class)
    private String city;

    @CsvCustomBindByPosition(position = 5, converter = StringQuoteTrimConverter.class)
    private String managerEmail;

    @CsvCustomBindByPosition(position = 6, converter = StringQuoteTrimConverter.class)
    private String organizationDescription;

    @CsvCustomBindByPosition(position = 7, converter = StringQuoteTrimConverter.class)
    private String offerDescription;

    @CsvCustomBindByPosition(position = 8, converter = WorkingTimeConverter.class)
    private WorkingTimeDTO workingTimeType;

    @CsvCustomBindByPosition(position = 9, converter = FrenchFloatConverter.class)
    private Float weeklyWorkingTime;

    @CsvCustomBindByPosition(position = 10, converter = FrenchFloatConverter.class)
    private Float contractDurationInMonth;

    @CsvCustomBindByPosition(position = 11, converter = FrenchFloatConverter.class)
    private Float minSalary;

    @CsvCustomBindByPosition(position = 12, converter = FrenchFloatConverter.class)
    private Float maxSalary;

    @CsvCustomBindByPosition(position = 13, converter = CustomRemoteWorkConverter.class)
    private String remoteWorkCriteria;

    @CsvCustomBindByPosition(position = 14, converter = WeekEndWorkingTimeConverter.class)
    private String weekendCriteria;

    @CsvCustomBindByPosition(position = 15, converter = NightWorkingTimeConverter.class)
    private String nightHoursCriteria;

    @CsvCustomBindByPosition(position = 16, converter = DateConverter.class)
    private OffsetDateTime startingDate;

    @CsvCustomBindByPosition(position = 17, converter = DiffusionTypeConverter.class)
    private DiffusionType diffusionType;

    public List<String> getCriteria() {
        return Stream.of(remoteWorkCriteria, weekendCriteria, nightHoursCriteria).filter(Objects::nonNull).toList();
    }
}
