package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.domain.enums.DiffusionType;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor
public class DiffusionTypeConverter extends AbstractBeanField<DiffusionType, String> {
    @Override
    protected DiffusionType convert(String rawValue) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        var value = StringUtils.trimToEmpty(rawValue).toUpperCase();

        return switch (value) {
            case "CV" -> DiffusionType.CV;
            case "HANDICAP" -> DiffusionType.HANDICAP;
            case "AUCUN" -> DiffusionType.NONE;
            default -> DiffusionType.BOTH;
        };
    }
}
