package com.erhgo.services;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.enums.DiffusionType;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.enums.WorkContractDurationUnit;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidCommandException;
import com.erhgo.domain.exceptions.NotPublishedRecruitmentException;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.sourcing.SourcingInvitation;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.GeneralInformationDTOBuilder;
import com.erhgo.services.dtobuilder.JobCandidatureDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.erhgo.services.dtobuilder.RecruitmentDTOBuilder;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.matching.MatchingService;
import com.erhgo.services.search.recruitment.RecruitmentIndexer;
import com.erhgo.services.userprofile.UserProfileProvider;
import com.erhgo.utils.DateTimeUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.JpaSort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RecruitmentService {
    private final NotificationRepository notificationRepository;
    private final SourcingInvitationRepository sourcingInvitationRepository;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final GeneralInformationRepository generalInformationRepository;
    private final RecruitmentProfileRepository recruitmentProfileRepository;
    private final RecruitmentRepository repository;
    private final UserProfileRepository userProfileRepository;
    private final ErhgoClassificationRepository erhgoClassificationRepository;

    private final MatchingService matchingService;
    private final JobService jobService;
    private final KeycloakService keycloakService;
    private final SecurityService securityService;

    private final JobCandidatureDTOBuilder jobCandidatureDTOBuilder;
    private final RecruitmentDTOBuilder recruitmentDTOBuilder;

    private final UserProfileProvider userProfileProvider;
    private final RecruitmentIndexer recruitmentIndexer;

    private static final float CAPACITY_THRESHOLD_DEFAULT = 0.9f;
    private static final float MASTERY_LEVEL_RANGE_QUERY_DEFAULT = 0.5f;
    @Value("${keycloak-realms.front_office_base_url}")
    private String foUrl;
    @Value("${application.handicapFoUrl}")
    private String handicapFoUrl;
    @Value("${application.socialLogoUrl}")
    private String logoUrl;
    @Value("${application.socialLogoHandicapUrl}")
    private String logoUrlHandicap;

    private Recruitment buildRecruitmentFromDTO(SaveRecruitmentCommandDTO command, RecruitmentProfile profile) {
        var recruitment = Recruitment.builder()
                .managerUserId(securityService.getAuthenticatedUserId())
                .recruitmentProfile(profile)
                .build();

        return updatedRecruitmentFromDTO(recruitment, command);
    }

    private Recruitment updatedRecruitmentFromDTO(Recruitment recruitment, SaveRecruitmentCommandDTO command) {
        return recruitment
                .setTypeContract(ContractType.valueOf(command.getTypeContract().name()))
                .setWorkContractDuration(command.getWorkContractDuration())
                .setWorkContractDurationUnit(command.getWorkContractDurationUnit() == null ? null : WorkContractDurationUnit.valueOf(command.getWorkContractDurationUnit().toString()))
                .setWorkingWeeklyTime(command.getWorkingWeeklyTime())
                .setLocation(command.getLocation() == null ? null : Location.buildLocationFromDTO(command.getLocation()))
                .setBaseSalary(command.getBaseSalary())
                .setMaxSalary(command.getMaxSalary())
                .setHideSalary(command.getHideSalary())
                .setExternalUrl(recruitment.getRecruiter().getOrganizationUrl())
                .setState(command.getState() == null ? null : RecruitmentState.valueOf(command.getState().name()), false)
                .setStartingDate(command.getStartingDate())
                .setPublicationDate(Calendar.getInstance().getTime())
                .setOrganizationDescription(command.getOrganizationDescription())
                .setDescription(command.getDescription())
                .setTitle(command.getTitle())
                .setUsersIdToNotify(command.getUsersIdToNotify() == null ? new HashSet<>() : new HashSet<>(command.getUsersIdToNotify()));
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentDTO create(SaveRecruitmentCommandDTO command) {
        var profileUUID = command.getRecruitmentProfileUuid();
        var profile = recruitmentProfileRepository.findById(profileUUID)
                .orElseThrow(() -> new EntityNotFoundException(profileUUID, RecruitmentProfile.class));

        if (!profile.isQualified()) {
            throw new InvalidCommandException("NOT_QUALIFIED_PROFILE", "Profile with id " + profileUUID + " is not qualified", "create recruitment");
        }

        var recruitment = repository.save(buildRecruitmentFromDTO(command, profile));
        recruitment.updateCodeOnJobCreate();

        if (profile.isModifiable()) {
            profile.setModifiable(false);
        }

        return recruitmentDTOBuilder.build(recruitment);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentDTO update(Long recruitmentId, SaveRecruitmentCommandDTO command) {
        var previous = repository.findById(recruitmentId).orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
        updatedRecruitmentFromDTO(previous, command);
        return recruitmentDTOBuilder.build(previous);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void changeState(String code, RecruitmentState state) {
        final var recruitment = repository.findOneByCode(code);
        if (state == RecruitmentState.PUBLISHED) {
            recruitment.publish();
        } else {
            recruitment.setState(state, false);
        }
    }


    @Transactional(readOnly = true)
    @RolesAllowed(value = Role.ODAS_ADMIN)
    public List<RecruitmentDTO> listRecruitmentsForJobId(UUID jobId) {
        return repository.findPublishedByJobId(jobId).stream().map(recruitmentDTOBuilder::build).toList();
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentForMatchingDTO getRecruitmentForMatching(Long recruitmentId) {
        var recruitment = getRecruitmentForMatchingInternal(recruitmentId);

        return RecruitmentDTOBuilder.buildForMatching(recruitment);
    }

    private Recruitment getRecruitmentForMatchingInternal(Long recruitmentId) {
        return repository.findById(recruitmentId)
                .orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_READ)
    public CandidatureSummaryDTO getMatchingCandidature(Long candidatureId) {
        var candidature = recruitmentCandidatureRepository.findById(candidatureId)
                .orElseThrow(() -> new EntityNotFoundException(candidatureId, RecruitmentCandidature.class));

        return jobCandidatureDTOBuilder.buildSummary(candidature, keycloakService.getFrontOfficeUserProfile(candidature.getUserProfile().userId()));
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public ContactForCandidatureDTO meetCandidature(Long candidatureId) {
        var candidature = recruitmentCandidatureRepository.findById(candidatureId)
                .orElseThrow(() -> new EntityNotFoundException(candidatureId, Recruitment.class));

        var generalInformation = generalInformationRepository.findByUserProfileUuid(candidature.getUserProfile().uuid());

        candidature.meet();

        return GeneralInformationDTOBuilder.buildContactForCandidature(candidature.getId(), candidature.getRefusalDate(), generalInformation, keycloakService.getFrontOfficeUserProfile(candidature.getUserProfile().userId()).orElseThrow(), candidature.isArchived());
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void refreshMatching(Long recruitmentId) {
        var recruitment = getRecruitmentOrThrow(recruitmentId);

        matchingService.refreshMatching(recruitment);
    }

    private Recruitment getRecruitmentOrThrow(Long recruitmentId) {
        return repository.findById(recruitmentId).orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public CandidatureContactInfoPageDTO getCandidatesForConsultant(Integer size,
                                                                    Integer page,
                                                                    Long recruitmentId,
                                                                    Boolean isMatch) {
        var recruitment = getRecruitmentOrThrow(recruitmentId);
        var candidatures = recruitmentCandidatureRepository.findByRecruitmentCodeAndValid(recruitment.getCode(), isMatch, size < 0 ? Pageable.unpaged() : PageRequest.of(page, size));
        var candidatureContactInfoPage = candidatures.map(c ->
                GeneralInformationDTOBuilder.buildContactForCandidature(c.getId(),
                        c.getRefusalDate(),
                        generalInformationRepository.findByUserProfileUuid(c.getUserProfile().uuid()),
                        keycloakService.getFrontOfficeUserProfile(c.getUserProfile().userId()).orElseThrow(),
                        c.isArchived())
        );
        return PageDTOBuilder.buildCandidatureContactInfoPage(candidatureContactInfoPage);
    }

    @Transactional(readOnly = true)
    public RecruitmentDetailDTO findDetailByCode(String recruitmentCode) {
        var recruitment = repository.findOneByCode(recruitmentCode);
        if (recruitment == null) {
            throw new EntityNotFoundException(recruitmentCode, Recruitment.class);
        }

        if (recruitment.getState() != RecruitmentState.PUBLISHED) {
            throw new NotPublishedRecruitmentException("Recruitment " + recruitmentCode + " is not accessible");
        }
        if (securityService.isAuthenticated() && securityService.getAuthenticatedUserId() != null) {
            var userProfile = userProfileProvider.getAuthenticatedUserProfileOrCreate();
            userProfile.ensureUserHasAccessToJob(recruitment.getJob());
        }
        return recruitmentDTOBuilder.buildDetail(recruitment);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentPageDTO list(
            String organizationCode,
            List<String> selectedProjects,
            boolean withNewCandidaturesOnly,
            boolean withOpenRecruitmentOnly,
            boolean internal,
            String query,
            Integer page,
            Integer size,
            RecruitmentSortDTO by,
            SortDirectionDTO direction,
            OrganizationTypeDTO organizationType
    ) {
        var realBy = RecruitmentRepository.RecruitmentSort.valueOf(by.name()).getBy();
        var hasFilterOrganization = !Strings.isNullOrEmpty(organizationCode);
        var organizationCodes = hasFilterOrganization ? keycloakService.getRolesForGroup(organizationCode) : Sets.<String>newHashSet();
        if (selectedProjects != null && !selectedProjects.isEmpty()) {
            if (hasFilterOrganization) {
                organizationCodes.retainAll(selectedProjects);
            } else {
                organizationCodes.addAll(selectedProjects);
                hasFilterOrganization = true;
            }
        }
        var recruitments = repository.findWithCandidatureCountByOrganizationCodeIn(
                organizationCodes,
                withNewCandidaturesOnly,
                withOpenRecruitmentOnly,
                Optional.ofNullable(organizationType).map(Enum::name).map(AbstractOrganization.OrganizationType::valueOf).orElse(null),
                internal,
                hasFilterOrganization,
                null,
                null,
                query,
                PageRequest.of(page, size, JpaSort.unsafe(direction == null ? Sort.DEFAULT_DIRECTION : Sort.Direction.fromString(direction.name()), realBy)));
        var recruitmentIds = recruitments.stream().map(r -> r.getRecruitment().getId()).collect(Collectors.toSet());
        var notificationsRecruitment = repository.findNotificationsForRecruitment(recruitmentIds);
        var sourcingInvitations = sourcingInvitationRepository.findBySubscriptionsRecruiterIn(recruitments.stream().map(r -> r.getRecruitment().getRecruiter()).collect(Collectors.toSet()));
        return PageDTOBuilder.buildRecruitmentPage(
                recruitments
                        .map(r -> getRecruitmentSummaryDTO(notificationsRecruitment, sourcingInvitations, r)));
    }

    private RecruitmentSummaryDTO getRecruitmentSummaryDTO(List<RecruitmentRepository.NotificationCount> notificationsRecruitment, Collection<SourcingInvitation> sourcingInvitations, RecruitmentRepository.RecruitmentWithCandidatureCount r) {
        var notificationsCount = notificationsRecruitment
                .stream()
                .filter(n -> n.getRecruitmentId().equals(r.getRecruitment().getId()))
                .findFirst()
                .orElse(null);

        var sourcingInvitationHost = sourcingInvitations.stream()
                .filter(h -> h.getGuests().stream().anyMatch(g -> g.getCode().equals(r.getRecruitment().getRecruiterCode())))
                .findFirst()
                .map(SourcingInvitation::getHostTitle)
                .orElse("");

        return createRecruitmentSummaryDTO(r, notificationsCount, sourcingInvitationHost);
    }

    private RecruitmentSummaryDTO createRecruitmentSummaryDTO(RecruitmentRepository.RecruitmentWithCandidatureCount r,
                                                              RecruitmentRepository.NotificationCount n,
                                                              String sourcingInvitationHostTitle) {
        var employerTitle = Optional.ofNullable(r.getRecruitment().getJob().getEmployerTitle()).orElse("");
        var recruiter = Optional.ofNullable(r.getRecruitment().getJob().getRecruiterTitle()).orElse("");
        var emailNotifications = n != null ? n.getEmailNotificationsCount() : 0;
        var phoneNotifications = n != null ? n.getMobileNotificationsCount() : 0;
        return RecruitmentDTOBuilder.buildSummary(
                r,
                employerTitle,
                recruiter,
                sourcingInvitationHostTitle,
                Math.toIntExact(emailNotifications),
                Math.toIntExact(phoneNotifications));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentPageForCandidateDTO listRecruitmentsForCandidate(UUID jobId, String userId, Integer page, Integer size) {
        var userProfile = getUserProfileOrThrow(userId);
        var recruitments = repository.findWithCandidatureByJobId(jobId, userProfile, size < 0 ? Pageable.unpaged() : PageRequest.of(page, size));
        var userLocation = Optional.ofNullable(userProfile.generalInformation()).map(GeneralInformation::getLocation);
        var recruitmentIds = recruitments.stream()
                .map(RecruitmentRepository.RecruitmentWithCandidatureForJob::getRecruitment)
                .map(Recruitment::getId)
                .toList();
        var recruitmentDistances = repository.findWithUserDistance(
                userLocation.map(Location::getLatitude).orElse(null),
                userLocation.map(Location::getLongitude).orElse(null),
                recruitmentIds
        );
        return PageDTOBuilder.buildRecruitmentPageForCandidate(recruitments.map(r -> createRecruitmentSummaryForCandidateDTO(r, recruitmentDistances)));
    }

    private UserProfile getUserProfileOrThrow(String userId) {
        return userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
    }

    private RecruitmentSummaryForCandidateDTO createRecruitmentSummaryForCandidateDTO(RecruitmentRepository.RecruitmentWithCandidatureForJob r, List<RecruitmentRepository.RecruitmentWithUserDistance> recruitmentDistances) {
        var distance = recruitmentDistances.stream()
                .filter(x -> r.getRecruitment().getId().equals(x.getRecruitmentId()))
                .map(RecruitmentRepository.RecruitmentWithUserDistance::getUserDistance)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        return RecruitmentDTOBuilder.buildSummaryForCandidate(
                r.getRecruitment(),
                r.getCandidature() != null && r.getCandidature().getValid(),
                r.getCandidature() != null && r.getCandidature().isRefused(),
                distance);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentDTO getRecruitmentForUpdate(String code) {
        var recruitment = repository.findOneByCode(code);
        if (recruitment == null) {
            throw new EntityNotFoundException(code, Recruitment.class);
        }
        return recruitmentDTOBuilder.build(recruitment);
    }

    @Transactional(readOnly = true)
    public SimpleRecruitmentPageDTO getRecruitments(Integer page, Integer size, String query) {
        Page<RecruitmentRepository.SimpleRecruitment> result;
        var userId = StringUtils.trimToNull(securityService.getAuthenticatedUserId());
        var userProfile = Optional.ofNullable(userId).flatMap(userProfileRepository::findByUserId).orElse(null);
        if (userProfile == null) {
            result = repository.findRecruitments(RecruitmentState.PUBLISHED, PageRequest.of(page, size));
        } else {
            var jobMaxLevel = jobService.computeMaxJobMasteryLevel(userProfile, MASTERY_LEVEL_RANGE_QUERY_DEFAULT);
            var candidateLocation = userProfile.generalInformation().getLocation();
            result = repository.findRecruitments(
                    jobMaxLevel,
                    userProfile.getAllCapacities(),
                    (int) (CAPACITY_THRESHOLD_DEFAULT * 100),
                    RecruitmentState.PUBLISHED,
                    userProfile,
                    userProfile.getRefusedCriteriaValues(),
                    PageRequest.of(page, size),
                    candidateLocation,
                    userProfile.getRefusedErhgoClassifications(),
                    userProfile.getBlacklistedOccupations(),
                    query
            );
        }

        return buildRecruitmentPageDTO(result);
    }

    @Transactional(readOnly = true)
    public SimpleRecruitmentCountDTO getRecruitmentsCount(String query) {
        var userId = securityService.getAuthenticatedUserId();
        Integer result = null;
        if (!StringUtils.isBlank(userId)) {
            var userProfile = userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
            result = repository.findRecruitmentsCount(
                    RecruitmentState.PUBLISHED,
                    userProfile,
                    userProfile.getRefusedCriteriaValues(),
                    userProfile.getRefusedErhgoClassifications(),
                    userProfile.getBlacklistedOccupations(),
                    query
            );
        }

        return new SimpleRecruitmentCountDTO().value(result);
    }

    private SimpleRecruitmentPageDTO buildRecruitmentPageDTO(Page<RecruitmentRepository.SimpleRecruitment> result) {
        return PageDTOBuilder.buildSimpleRecruitmentPage(result.map(recruitmentDTOBuilder::buildRecruitmentSummary));
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_OF_COMMAND_WRITE)
    public void updateErhgoQualificationForRecruitment(SetRecruitmentErhgoClassificationsCommandDTO command) {
        var recruitment = repository.findById(command.getRecruitmentId()).orElseThrow(() -> new EntityNotFoundException(command.getRecruitmentId(), Recruitment.class));
        if (command.getErhgoClassificationCodes().isEmpty()) {
            recruitment.clearErhgoClassifications();
        } else {
            var classifications = erhgoClassificationRepository.findErhgoClassificationByCodeIn(command.getErhgoClassificationCodes());
            recruitment.resetErhgoClassifications(classifications);
        }
    }

    @Transactional
    public List<UserRecruitmentReportItemDTO> createAndListUserRecruitmentReports(String userId) {
        var userProfile = userProfileRepository.findByUserId(userId).orElseThrow();
        var candidatures = recruitmentCandidatureRepository.findByUserProfileUserId(userId);
        var recruitments = candidatures.stream().map(RecruitmentCandidature::getRecruitment).collect(Collectors.toCollection(HashSet::new));
        var notifiedRecruitments = notificationRepository.findRecruitmentNotificationByUserProfileUuid(userProfile.uuid());
        notifiedRecruitments.stream().map(RecruitmentNotification::getRecruitment).forEach(recruitments::add);
        return recruitments
                .stream()
                .map(r ->
                        new UserRecruitmentReportItemDTO()
                                .recruitmentId(r.getId())
                                .recruitmentTitle(r.getJob().getTitle())
                                .recruiterName(r.getRecruiterTitle())
                                .recruiterCode(r.getRecruiterCode())
                                .recruiterType(OrganizationTypeDTO.fromValue(r.getRecruiterType().name()))
                                .candidature(getCandidatureForUserRecruitmentReport(candidatures, r))
                                .notifications(getNotificationsForUserRecruitmentReport(notifiedRecruitments, r))
                )
                .toList();
    }

    private CandidatureForRecruitmentReportItemDTO getCandidatureForUserRecruitmentReport(Collection<RecruitmentCandidature> candidatures, Recruitment recruitment) {
        return candidatures
                .stream()
                .filter(c -> c.getRecruitment().getId().equals(recruitment.getId())).map(c ->
                        new CandidatureForRecruitmentReportItemDTO()
                                .id(c.getId())
                                .publishedAt(c.getSubmissionDate())
                                .generatedFromSourcing(c.isGeneratedForSourcing())
                                .visibleForUser(c.isVisibleForUser())
                                .archived(c.isArchived())
                                .state(Optional.ofNullable(c.getGlobalCandidatureState()).map(o -> CandidatureStateDTO.fromValue(o.name())).orElse(null)))
                .findFirst()
                .orElse(null);
    }

    private List<NotificationForRecruitmentReportItemDTO> getNotificationsForUserRecruitmentReport(Collection<RecruitmentNotification> notifiedRecruitments, Recruitment recruitment) {
        return notifiedRecruitments
                .stream()
                .filter(n -> n.getRecruitment().getId().equals(recruitment.getId()))
                .map(f ->
                        new NotificationForRecruitmentReportItemDTO()
                                .type(NotificationTypeDTO.fromValue(f.getType().name()))
                                .createdAt(DateTimeUtils.dateToLocalDate(f.getCreatedDate()))
                ).toList();
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void updateDiffusionType(Long recruitmentId, DiffusionTypeDTO diffusionType) {
        var recruitment = repository.findById(recruitmentId)
                .orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));

        recruitment.setDiffusionType(Optional.ofNullable(diffusionType).map(d -> DiffusionType.valueOf(d.name())).orElse(null));
        repository.save(recruitment);
        recruitmentIndexer.index(recruitment);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                log.debug("Reindexing recruitment {} after transaction commit", recruitment.getCode());
                recruitmentIndexer.index(recruitment);
            }
        });
    }

    @Transactional(readOnly = true)
    public Map<String, String> getOfferAttributesForSsr(String code, boolean isHandicap) {
        var recruitment = repository.findOneByCode(code);
        if (recruitment == null) {
            throw new EntityNotFoundException(code, Recruitment.class);
        }
        return Map.of(
                "title", "%s chez %s".formatted(recruitment.getJobTitle(), recruitment.getRecruiterTitle()),
                "description", "%s - %s (%s) - Nous attendons votre candidature !".formatted(recruitment.getTitle(), recruitment.getCity(), recruitment.getPostcode()),
                "image", isHandicap ? logoUrlHandicap : logoUrl,
                "url", (isHandicap ? handicapFoUrl : foUrl) + "/jobs/%s".formatted(code),
                "author", "Laura",
                "publishDate", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(recruitment.getPublicationDate())
        );
    }
}




