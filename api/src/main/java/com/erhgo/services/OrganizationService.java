package com.erhgo.services;

import com.erhgo.domain.enums.DiffusionType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Employer;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.AbstractOrganizationRepository;
import com.erhgo.repositories.EmployerRepository;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.CustomEmailTemplateDTOBuilder;
import com.erhgo.services.dtobuilder.OrganizationDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.search.recruitment.RecruitmentIndexer;
import com.erhgo.utils.StringUtils;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.StreamSupport;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrganizationService {
    private final KeycloakService keycloakService;
    private final SourcingKeycloakService sourcingKeycloakService;
    private final OrganizationDTOBuilder organizationDTOBuilder;
    private final CustomEmailTemplateDTOBuilder customEmailTemplateDTOBuilder;
    private final RecruiterRepository recruiterRepository;
    private final EmployerRepository employerRepository;
    private final SecurityService securityService;
    private final AbstractOrganizationRepository repository;
    private final RecruitmentRepository recruitmentRepository;
    private final RecruitmentIndexer recruitmentIndexer;


    private void createFrontOfficeGroupAndRoleIfRequired(AbstractOrganization organization) {
        keycloakService.createFrontOfficeGroupAndRole(organization.getCode());
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public OrganizationDTO findOrganizationByCode(String organizationCode) {
        var organization = organizationDTOBuilder.buildOrganizationDTO(repository.findOneByCode(organizationCode));

        if (organization == null) {
            throw new EntityNotFoundException(organizationCode, AbstractOrganization.class);
        }

        return organization;
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public Long count(String organizationCode) {
        if (Strings.isBlank(organizationCode)) {
            return recruiterRepository.count();
        } else {
            return employerRepository.count();
        }
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public OrganizationPageDTO findPaginatedAndFilteredByProperty(
            int offset,
            int limit,
            String sortProperty,
            SortDirectionDTO sortDirection,
            String filter,
            Collection<String> refererRecruiters) {
        var safeSortProperty = Strings.isBlank(sortProperty) ? "code" : sortProperty;
        final Pageable pageable = limit < 0 ? Pageable.unpaged() : PageRequest.of(
                offset,
                limit,
                Optional.ofNullable(sortDirection).map(s -> Sort.Direction.valueOf(s.name())).orElse(Sort.Direction.ASC),
                safeSortProperty
        );
        Page<? extends AbstractOrganization> result;
        if (refererRecruiters == null || refererRecruiters.isEmpty()) {
            result = fetchOrganizationPage(pageable, filter);
        } else {
            result = fetchOrganizationPageForRefererRecruiter(pageable, filter, recruiterRepository.findByCodeIn(refererRecruiters));
        }
        return buildOrganizationPageDTO(result);
    }

    protected Page<Employer> fetchOrganizationPageForRefererRecruiter(Pageable pageRequest, String filter, Collection<Recruiter> referers) {
        return employerRepository.findEmployers(Strings.trimToNull(filter), referers, pageRequest);
    }


    protected Page<Recruiter> fetchOrganizationPage(Pageable pageRequest, String filter) {
        if (filter != null && !filter.isEmpty()) {
            return recruiterRepository.findByCodeContainingIgnoreCaseOrTitleContainingIgnoreCase(filter, filter, pageRequest);
        } else {
            return recruiterRepository.findAll(pageRequest);
        }
    }


    private OrganizationPageDTO buildOrganizationPageDTO(Page<? extends AbstractOrganization> result) {
        return PageDTOBuilder
                .buildOrganizationPage(result.map(organizationDTOBuilder::buildOrganizationDTO));
    }


    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public <A extends AbstractOrganization> A save(SaveOrganizationCommandDTO command) {
        AbstractOrganization organization;

        var referer = fetchReferer(command);
        var defaultProject = fetchDefaultProject(command);

        var creation = command.getId() == null;
        if (creation) {
            organization = createOrganization(command, referer);
        } else {
            organization = repository.findById(command.getId())
                    .orElseThrow(() -> new EntityNotFoundException(command.getId(), Recruiter.class));
            organization.setRefererRecruiter(referer);
        }
        organization.setDefaultProject(defaultProject);
        organization.setDescription(command.getDescription());
        organization.setTitle(command.getTitle());
        organization.setAddress(command.getAddress());
        organization.setSiren(command.getSiren());
        organization.setSiret(command.getSiret());
        organization.setPrivateUsers(BooleanUtils.isTrue(command.getPrivateUsers()));
        organization.setPrivateJobs(BooleanUtils.isTrue(command.getPrivateJobs()));
        organization.setMandatoryIdentity(BooleanUtils.isTrue(command.getMandatoryIdentity()));
        organization.setInternal(BooleanUtils.isTrue(command.getInternal()));
        organization.setForcedUrl(command.getForcedUrl());
        if (command.getRefusalEmailTemplate() != null) {
            organization.setRefusalEmailTemplate(customEmailTemplateDTOBuilder.getCustomEmailTemplate(command.getRefusalEmailTemplate()));
        }

        if (organization instanceof Recruiter recruiter) {
            var oldDiffusionType = recruiter.getDefaultDiffusionType();
            var newDiffusionType = command.getDefaultDiffusionType() != null ?
                    DiffusionType.valueOf(command.getDefaultDiffusionType().name()) : DiffusionType.BOTH;

            if (oldDiffusionType != newDiffusionType) {
                recruiter.setDefaultDiffusionType(newDiffusionType);
                if (!creation) {
                    reindexRecruitmentsForRecruiter(recruiter);
                }
            }
        }

        repository.save(organization);
        if (creation) {
            organization.updateCodeOnEntityCreate();
        }

        var roles = Sets.<String>newHashSet();
        if (command.getProjectIds() != null && !command.getProjectIds().isEmpty() && organization.mayHaveProjects()) {
            StreamSupport.stream(recruiterRepository.findAllById(command.getProjectIds()).spliterator(), false)
                    .filter(e -> e.getOrganizationType() == AbstractOrganization.OrganizationType.PROJECT)
                    .forEach(e -> roles.add(e.getCode()));
        }

        if (command.getConsortiumIds() != null && organization.mayHaveConsortiums()) {
            var consortiums = recruiterRepository.findAllById(command.getConsortiumIds());
            organization.replaceConsortiums(Sets.newHashSet(consortiums));
        }
        keycloakService.createBackOfficeGroupAndRoles(organization.getCode(), roles.toArray(String[]::new));
        createFrontOfficeGroupAndRoleIfRequired(organization);
        if (creation && command.getOrganizationType() == OrganizationTypeDTO.SOURCING) {
            sourcingKeycloakService.createSourcingGroupAndRole(organization.getCode());
        }
        return (A) organization;
    }

    private Recruiter fetchReferer(SaveOrganizationCommandDTO saveOrganizationCommandDTO) {
        if (OrganizationTypeDTO.EMPLOYER != saveOrganizationCommandDTO.getOrganizationType()) {
            return null;
        }
        var referer = (Recruiter) repository.findOneByCode(saveOrganizationCommandDTO.getRefererRecruiterCode());
        if (referer == null) {
            throw new EntityNotFoundException(saveOrganizationCommandDTO.getRefererRecruiterCode(), Recruiter.class);
        }

        return referer;
    }

    private Recruiter fetchDefaultProject(SaveOrganizationCommandDTO saveOrganizationCommandDTO) {
        switch (saveOrganizationCommandDTO.getOrganizationType()) {
            case CONSORTIUM:
            case TERRITORIAL:
                var defaultProjectId = saveOrganizationCommandDTO.getDefaultProjectId();
                if (defaultProjectId == null) {
                    return null;
                }
                return (Recruiter) repository.findById(defaultProjectId).orElseThrow(() -> new EntityNotFoundException(defaultProjectId, Recruiter.class));
            default:
                return null;
        }
    }

    private AbstractOrganization createOrganization(SaveOrganizationCommandDTO command, Recruiter referer) {
        AbstractOrganization organization;
        if (OrganizationTypeDTO.EMPLOYER == command.getOrganizationType()) {
            organization = Employer.employerBuilder()
                    .refererRecruiter(referer)
                    .build();
        } else {
            var recruiterBuilder = Recruiter.recruiterBuilder()
                    .organizationType(AbstractOrganization.OrganizationType.valueOf(command.getOrganizationType().name()));
            organization = recruiterBuilder.build();
        }
        return organization;
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public OrganizationDTO getOrganization(Long id) {
        return repository.findById(id)
                .map(organizationDTOBuilder::buildOrganizationDTO)
                .orElseThrow(() -> new EntityNotFoundException(id, Recruiter.class))
                ;
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<OrganizationSummaryDTO> getAllRecruiters() {
        var isAdmin = securityService.isAdmin();
        return StreamSupport.stream(recruiterRepository
                        .findAll().spliterator(), false)
                .filter(e -> isAdmin || securityService.hasAuthenticatedUserRole(e.getCode()))
                .sorted(Comparator.comparing(AbstractOrganization::getTitle,
                        StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE))
                .map(organizationDTOBuilder::buildOrganizationSummary)
                .toList();
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<OrganizationSummaryDTO> getAllRecruitersForOrganization(String organizationCode) {
        var roles = keycloakService.getRolesForGroup(organizationCode);
        return organizationDTOBuilder.fetchAndBuildSummary(roles);
    }


    public List<OrganizationSummaryDTO> findOrganizationsByCodes(List<String> codes) {
        return repository.findByCodeIn(codes).stream().map(organizationDTOBuilder::buildOrganizationSummary).toList();
    }

    private void reindexRecruitmentsForRecruiter(Recruiter recruiter) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                log.info("Reindexing recruitments for recruiter {} after transaction commit", recruiter.getCode());
                recruitmentIndexer.indexRecruitmentsForRecruiter(recruiter);
            }
        });
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public void reindexRecruiterRecruitments(String recruiterCode) {
        var recruiter = recruiterRepository.findOneByCode(recruiterCode);
        if (recruiter == null) {
            throw new EntityNotFoundException(recruiterCode, Recruiter.class);
        }

        log.info("Reindexing all recruitments for recruiter {}", recruiterCode);
        recruitmentIndexer.indexRecruitmentsForRecruiter(recruiter);
        log.info("Reindexation completed for recruiter {}", recruiterCode);
    }
}
