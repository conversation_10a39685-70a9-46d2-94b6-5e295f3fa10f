package com.erhgo.services.sourcing;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.exceptions.*;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.RecruitmentCreationState;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.JobType;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.domain.sourcing.SourcingSubscription;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.ErhgoCompositePermissionEvaluator;
import com.erhgo.security.PermissionLevel;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.SourcingDTOBuilder;
import com.erhgo.services.dtobuilder.SourcingUserDTOBuilder;
import com.erhgo.services.externaloffer.notification.RemoteOfferErrorDTO;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.generation.dto.OccupationForLabelGenerationResult;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.ClosedRecruitmentMailService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.services.notifier.messages.SourcingDisabledAccountMessageDTO;
import com.erhgo.services.notifier.messages.SourcingJobOnOccupationMessage;
import com.erhgo.services.notifier.messages.SourcingPublishRecruitmentMessageDTO;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SourcingJobRecruitmentService {

    private static final int SOURCING_RECRUITMENT_SUMMARY_STEP = 5;
    private final JobRepository jobRepository;
    private final RecruitmentRepository recruitmentRepository;
    private final SourcingUserSearchService sourcingUserSearchService;
    private final RecruitmentProfileRepository recruitmentProfileRepository;
    private final CriteriaRepository criteriaRepository;
    private final SourcingPreferencesRepository preferencesRepository;
    private final SourcingUserDTOBuilder sourcingUserDTOBuilder;
    private final SourcingDTOBuilder sourcingDTOBuilder;
    private final SecurityService securityService;
    private final SourcingKeycloakService keycloakService;
    private final SourcingService sourcingService;
    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final CategoryRepository categoryRepository;
    private final ErhgoCompositePermissionEvaluator erhgoCompositePermissionEvaluator;
    private final ExternalOfferRepository externalOfferRepository;
    private final Notifier notifier;
    private final SourcingMailingService sourcingMailingService;
    private final UserMobileTokenRepository userMobileTokenRepository;
    private final SourcingSubscriptionRepository sourcingSubscriptionRepository;
    private final SourcingUserRepository sourcingUserRepository;
    private final ClosedRecruitmentMailService closedRecruitmentMailService;
    private final SourcingCandidateCriteriaBuilder sourcingCandidateCriteriaBuilder;
    private final OccupationForLabelGenerationService occupationForLabelGenerationService;
    private final RecruiterRepository recruiterRepository;
    @Value("${application.sourcing.trialDurationInDays}")
    private int trialDurationInDays;
    @Value("${application.sourcing.recruitmentLifespanInDays}")
    private int recruitmentLifespanInDays;
    @Value("${ats.adecco.forcedSlackChannel}")
    private String adeccoForcedSlackChannel;

    @Transactional
    @PreAuthorize(AuthorizeExpression.JOB.JOB_WRITE)
    public void updateContractOfJob(UUID jobId, UpdateSourcingJobContractCommandDTO command) {
        var job = jobRepository.findById(jobId).orElseThrow(() -> new EntityNotFoundException(jobId, Job.class));
        var recruitment = recruitmentRepository.findFirstByRecruitmentProfileJobId(jobId);

        if (recruitment == null) {
            log.error("No recruitment for sourcing jobId {}", jobId);
            throw new GenericTechnicalException("No recruitment for sourcing jobId " + jobId);
        }
        var typeContractCategory = TypeContractCategory.valueOf(command.getTypeContractCategory().name());
        recruitment
                .setTypeContract(ContractType.forCategory(typeContractCategory))
                .setBaseSalary(command.getBaseSalary())
                .setMaxSalary(command.getMaxSalary())
                .setHideSalary(command.getHideSalary())
                .setModularWorkingTime(command.getModularWorkingTime())
                .setWorkingWeeklyTime(command.getWorkingWeeklyTime());
        setContractTypeAndWorkingTime(
                job,
                typeContractCategory,
                TypeWorkingTime.valueOf(command.getWorkingTimeType().name()));

    }

    public void setContractTypeAndWorkingTime(Job job, TypeContractCategory typeContractCategory, TypeWorkingTime typeWorkingTime) {
        var contractTypes = job.getContractTypes();
        var workingTimes = job.getWorkingTimes();
        if (
                contractTypes.size() != 1
                        || !contractTypes.contains(typeContractCategory)
                        || workingTimes.size() != 1
                        || !workingTimes.contains(typeWorkingTime)
        ) {
            var criteriaValues = new HashSet<>(job.getCriteriaValues());
            var contractTypeCodes = CriteriaValue.TYPE_CONTRACT_FOR_CRITERIA_RESPONSE.keySet();
            var workingTimeCodes = CriteriaValue.WORKING_TIME_FOR_CRITERIA_RESPONSE.keySet();
            criteriaValues.removeIf(c -> contractTypeCodes.contains(c.getCode()) || workingTimeCodes.contains(c.getCode()));
            criteriaValues.add(criteriaRepository.findCriteriaValueByCode(CriteriaValue.getValueCodeForTypeContractCategory(typeContractCategory)));
            criteriaValues.add(criteriaRepository.findCriteriaValueByCode(CriteriaValue.getValueCodeForTypeWorkingTime(typeWorkingTime)));
            job.resetCriteriaValues(criteriaValues);
        }
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_WRITE)
    public SourcingJobAndRecruitmentDTO getSourcingJobAndRecruitment(Long recruitmentId) {
        var recruitment = getRecruitmentOrThrow(recruitmentId);
        return sourcingDTOBuilder.buildSourcingJobAndRecruitmentDTO(recruitment);
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_WRITE)
    public SourcingJobAndRecruitmentDTO duplicateSourcingJobAndRecruitment(Long recruitmentId) {
        var recruitmentToDuplicate = getRecruitmentOrThrow(recruitmentId);
        var duplicatedJob = recruitmentToDuplicate.getJob().duplicates();
        jobRepository.save(duplicatedJob);
        var duplicatedRecruitmentProfile = RecruitmentProfile.buildWithAllOptional(duplicatedJob, recruitmentToDuplicate.getCustomQuestion());
        recruitmentProfileRepository.save(duplicatedRecruitmentProfile);
        var duplicatedRecruitment = recruitmentToDuplicate.duplicates(duplicatedRecruitmentProfile, securityService.getAuthenticatedUserId());
        duplicatedRecruitment = recruitmentRepository.save(duplicatedRecruitment);
        duplicatedRecruitment.updateCodeOnJobCreate();
        return sourcingDTOBuilder.buildSourcingJobAndRecruitmentDTO(duplicatedRecruitment);
    }

    private Recruitment getRecruitmentOrThrow(Long recruitmentId) {
        return recruitmentRepository.findById(recruitmentId).orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_WRITE)
    public void updateUsersToNotify(List<String> userIds, Long recruitmentId) {
        Optional.ofNullable(recruitmentId)
                .map(this::getRecruitmentOrThrow)
                .ifPresentOrElse(
                        r -> r.setSourcingUsersIdToNotify(Sets.newHashSet(userIds)),
                        () -> updateSpontaneousCandidaturesNotifiedUsers(userIds)
                );
    }

    private void updateSpontaneousCandidaturesNotifiedUsers(List<String> userIds) {
        var allUsersInOrganization = keycloakService.getEnabledSourcingUsersForGroup(sourcingService.getAuthenticatedRecruiter().getCode()).stream().map(UserRepresentation::getId).collect(Collectors.toSet());
        var alreadyExistantUsersPreferences = preferencesRepository.findByUserIdIn(allUsersInOrganization);
        alreadyExistantUsersPreferences.forEach(p -> p.notifyOnSpontaneousCandidature(userIds.contains(p.userId())));
        var newUsersPreferences = userIds.stream()
                .filter(u -> alreadyExistantUsersPreferences.stream().noneMatch(p -> p.userId().equals(u)))
                .map(u -> SourcingPreferences.fromDefault(u).notifyOnSpontaneousCandidature(true))
                .toList();
        preferencesRepository.saveAll(newUsersPreferences);
    }

    @Transactional
    @RolesAllowed(Role.SOURCING)
    public List<SourcingUserDTO> getUsersToNotifyOnSpontaneousCandidature() {
        var code = sourcingService.getAuthenticatedRecruiter().getCode();
        if (code != null) {
            var users = buildUserRepresentationForUserIdMap(code);
            var prefs = preferencesRepository.findByUserIdInAndNotifyOnSpontaneousCandidatureIsTrue(users.keySet());
            return prefs.stream().map(p -> users.get(p.userId())).map(sourcingUserDTOBuilder::buildSourcingUser).toList();
        }
        return Collections.emptyList();
    }

    @NotNull
    private Map<String, UserRepresentation> buildUserRepresentationForUserIdMap(String code) {
        return keycloakService.getEnabledSourcingUsersForGroup(code).stream().collect(Collectors.toMap(UserRepresentation::getId, Function.identity()));
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_OF_COMMAND_WRITE)
    public void updateManagerOfRecruitment(UpdateManagerCommandDTO command) {
        var recruitment = getRecruitmentOrThrow(command.getRecruitmentId());
        keycloakService.getSourcingUser(command.getManagerUserId())
                .ifPresentOrElse(
                        u -> recruitment.setManagerUserId(command.getManagerUserId()),
                        () -> {
                            throw new EntityNotFoundException(command.getManagerUserId(), UserRepresentation.class);
                        }
                );

    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.ORGANIZATION.ORGANIZATION_READ)
    public Long createOrUpdateRecruitmentForBatch(String organizationCode, CreateOrUpdateFullRecruitmentCommandDTO command, String sourceType, List<String> refererEmailWithManagerFirst) {
        var recruitment = createOrUpdateRecruitmentInternal(recruiterRepository.findOneByCode(organizationCode), command);
        recruitment.setSendNotificationState(RecruitmentSendNotificationState.CANCEL);
        recruitment.setSource(sourceType);

        computeManagerAndReferrers(refererEmailWithManagerFirst, recruitment);

        var recruitmentId = recruitment.getId();
        var success = recruitmentId != null;
        var externalOfferId = command.getExternalOfferId();
        var isModification = command.getRecruitmentId() != null;
        if (externalOfferId != null) {
            var externalOffer = externalOfferRepository.findById(externalOfferId)
                    .orElseThrow(() -> new EntityNotFoundException(externalOfferId, ExternalOffer.class));

            externalOffer.setRecruitmentCreationState(success ?
                    RecruitmentCreationState.DONE :
                    RecruitmentCreationState.ERROR);
        }
        RemoteOfferErrorDTO message;
        var forcedSlackChannel = Optional.ofNullable(recruitment.getExternalOffer()).map(ExternalOffer::getAtsCode).filter("adecco"::equalsIgnoreCase).map(a -> adeccoForcedSlackChannel).orElse(null);
        if (!success) {
            if (externalOfferId != null) {
                message = new RemoteOfferErrorDTO(externalOfferId, command.getTitle(), sourceType, isModification, forcedSlackChannel);
            } else {
                message = new RemoteOfferErrorDTO(recruitmentId, command.getTitle(), sourceType, isModification, forcedSlackChannel);
            }
            notifier.sendMessage(message);
        }
        return recruitmentId;
    }

    private void computeManagerAndReferrers(List<String> refererEmailWithManagerFirst, Recruitment recruitment) {
        var recruiterCode = recruitment.getRecruiterCode();
        String managerId = null;
        var refererIds = refererEmailWithManagerFirst
                .stream()
                .map(keycloakService::getSourcingUserFromEmail)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(UserRepresentation::getId)
                .toList();

        if (!refererIds.isEmpty()) {
            managerId = refererIds.getFirst();
        }
        if (managerId == null) {
            managerId = sourcingUserSearchService.findDefaultManagerFor(recruitment);
            if (managerId != null) {
                refererIds = List.of(managerId);
            }
            log.debug("No user found on offer for recruitment id {} - using default {} (recruiter: {})", recruitment.getId(), managerId, recruiterCode);
        }
        if (managerId == null) {
            recruitment.setSourcingUsersIdToNotify(Collections.emptySet());
            log.warn("Definitely no user found on recruitment id {} on organization {} - nobody will be notified", recruitment.getId(), recruiterCode);
        } else {
            recruitment.setManagerUserId(managerId);
            recruitment.setSourcingUsersIdToNotify(new HashSet<>(refererIds));
        }
    }

    @RolesAllowed(Role.SOURCING)
    @Transactional
    public Long createOrUpdateRecruitmentWithFormCommand(CreateOrUpdateFullRecruitmentCommandDTO command) {
        return createOrUpdateRecruitmentInternal(sourcingService.getAuthenticatedRecruiter(), command).getId();
    }

    private Recruitment createOrUpdateRecruitmentInternal(Recruiter recruiter, CreateOrUpdateFullRecruitmentCommandDTO command) {
        var recruitment = command.getRecruitmentId() != null ? getRecruitmentOrThrow(command.getRecruitmentId()) : null;
        var jobOccupationId = command.getJobOccupationId();
        var title = command.getTitle();
        var occupation = getErhgoOccupationOrCreateForTitle(jobOccupationId, title);

        var allCategories = categoryRepository.findAll();
        if (recruitment == null) {
            var job = jobRepository.save(Job.createForErhgoOccupation(occupation, recruiter, allCategories, JobType.SOURCING));
            recruitment = createNewSourcingRecruitment(job);
            recruitment.setSourcingStep(SOURCING_RECRUITMENT_SUMMARY_STEP);
        }
        addFormValuesToRecruitment(command, recruitment);
        addFormValuesToJob(command, recruitment.getJob());
        recruitment = saveRecruitment(recruitment);
        var externalOfferId = command.getExternalOfferId();
        if (externalOfferId != null) {
            var externalOffer = externalOfferRepository.findById(externalOfferId).orElseThrow(() -> new EntityNotFoundException(externalOfferId, ExternalOffer.class));
            if (externalOffer.getRecruitment() == null) {
                externalOffer.setRecruitment(recruitment);
            }
        }
        return recruitment;
    }

    private ErhgoOccupation getErhgoOccupationOrCreateForTitle(UUID jobOccupationId, String title) {
        return Optional.ofNullable(jobOccupationId)
                .or(() -> Optional
                        .of(occupationForLabelGenerationService.createOrUpdateOccupation(title, OccupationCreationSourceType.FROM_SOURCING))
                        .filter(r -> !r.inError())
                        .map(OccupationForLabelGenerationResult::uuid)
                )
                .flatMap(erhgoOccupationRepository::findById)
                .filter(ErhgoOccupation::hasAnyCapacity)
                .orElseThrow(() -> new InvalidJobTitleException("Unable to create recruitment - no capacity or invalid title for occupation %s".formatted(title)));
    }

    private void addFormValuesToJob(CreateOrUpdateFullRecruitmentCommandDTO command, Job job) {
        if (!command.getCriteriaValues().isEmpty()) {
            var criteria = criteriaRepository.findCriteriaValuesByCriteriaValuesCodeIn(command.getCriteriaValues());
            job.resetCriteriaValues(criteria, null);
        } else {
            job.clearCriteria(null);
        }
        var typeContractCategory = TypeContractCategory.valueOf(command.getTypeContractCategory().name());
        var typeWorkingTime = TypeWorkingTime.valueOf(command.getWorkingTimeType().name());
        job.setTitle(command.getTitle());
        setContractTypeAndWorkingTime(job, typeContractCategory, typeWorkingTime);
    }

    private void addFormValuesToRecruitment(CreateOrUpdateFullRecruitmentCommandDTO command, Recruitment recruitment) {
        var typeContractCategory = TypeContractCategory.valueOf(command.getTypeContractCategory().name());
        var jobDescription = StringUtils.defaultIfBlank(command.getDescription(), recruitment.getRecruitmentOrOccupationDescription());
        var organizationDescription = StringUtils.defaultIfBlank(command.getOrganizationDescription(), recruitment.getOrganizationOrRecruiterDescription());
        recruitment
                .setLocation(Location.buildLocationFromDTO(command.getJobLocation()))
                .setTypeContract(ContractType.forCategory(typeContractCategory))
                .setBaseSalary(command.getBaseSalary())
                .setMaxSalary(command.getMaxSalary())
                .setModularWorkingTime(command.getModularWorkingTime())
                .setWorkingWeeklyTime(command.getWorkingWeeklyTime())
                .setTitle(buildRecruitmentTitle(command.getTitle()))
                .setDescription(jobDescription)
                .setOrganizationDescription(organizationDescription)
                .setHideSalary(command.getHideSalary())
                .setWorkContractDuration(command.getWorkContractDuration())
                .setWorkContractDurationUnit(Optional.ofNullable(command.getWorkContractDurationUnit()).map(Enum::name).map(WorkContractDurationUnit::valueOf).orElse(null))
                .setHideSalary(command.getHideSalary())
                .setStartingDate(command.getStartingDate())
        ;
        if (command.getDiffusionType() != null) {
            recruitment.setDiffusionType(DiffusionType.valueOf(command.getDiffusionType().name()));
        }
    }

    private ErhgoOccupation getOccupationOrThrow(UUID occupationId) {
        return erhgoOccupationRepository.findById(occupationId)
                .orElseThrow(() -> new EntityNotFoundException(occupationId, ErhgoOccupation.class));
    }

    @Transactional
    @RolesAllowed(Role.SOURCING)
    public SourcingJobAndRecruitmentDTO createOrUpdateSourcingJobAndRecruitment(CreateSourcingJobAndRecruitmentCommandDTO command) {
        var recruiter = sourcingService.getAuthenticatedRecruiterAndEnsureCanCreateRecruitment();
        var recruitment = createOrUpdateRecruitment(command.getJobLocation(), command.getJobOccupationId(), recruiter, command.getRecruitmentId(), command.getTitle());
        return sourcingDTOBuilder.buildSourcingJobAndRecruitmentDTO(recruitment);
    }

    private Recruitment createOrUpdateRecruitment(
            LocationDTO locationDTO,
            UUID occupationId,
            Recruiter organization,
            Long recruitmentIdToUpdate,
            String title) {
        Job job;
        if (locationDTO == null) {
            return null;
        }
        var recruitment = recruitmentIdToUpdate == null ? null : getRecruitmentByIdForModificationOrThrow(recruitmentIdToUpdate);
        // next line is a bit redundant - the idea: if occupationId != null => throws if not found, else create it
        var occupation = Optional.ofNullable(occupationId).map(this::getOccupationOrThrow).orElseGet(() -> getErhgoOccupationOrCreateForTitle(occupationId, title));
        var sendNotificationOnRecruitment = true;
        var allCategories = categoryRepository.findAll();

        if (recruitment == null) {
            verifyTrialLimit(organization);
            job = jobRepository.save(Job.createForErhgoOccupation(occupation, organization, allCategories, JobType.SOURCING));
            recruitment = createNewSourcingRecruitment(job);
        } else {
            if (recruitment.getState() != RecruitmentState.DRAFT) {
                log.warn("Ignoring modification on non draft recruitment");
                return recruitment;
            }
            var existentJob = recruitment.getJob();
            sendNotificationOnRecruitment = !existentJob.hasOccupation(occupation);
            job = existentJob.reworkWithErhgoOccupation(occupation, allCategories);
            recruitment.getRecruitmentProfile().markAllOptional();
        }
        updateJobTitle(title, job);
        updateJobAndRecruitmentLocation(locationDTO, job, recruitment);
        recruitment = saveRecruitment(recruitment);
        if (sendNotificationOnRecruitment) {
            sendRecruitmentNotification(organization, title, recruitment, occupation);
        }
        return recruitment;
    }

    private Recruitment getRecruitmentByIdForModificationOrThrow(Long recruitmentIdToUpdate) {
        var recruitment = getRecruitmentOrThrow(recruitmentIdToUpdate);
        if (!erhgoCompositePermissionEvaluator.hasPermission(securityService.getAuthentication(), recruitment, PermissionLevel.READ)) {
            log.warn("authenticated user is not allowed to modify {}", recruitment);
            throw new UserNotAllowedForEntity("User is not allowed");
        }
        if (recruitment.getState() == RecruitmentState.PUBLISHED) {
            throw new UserNotAllowedForEntity("Recruitment %d is published and not modifiable".formatted(recruitmentIdToUpdate));
        }
        return recruitment;
    }

    private void verifyTrialLimit(Recruiter recruiter) {
        var authenticatedUserSubscription = sourcingService.getAuthenticatedUserSubscription();
        if (
                authenticatedUserSubscription != null
                        && authenticatedUserSubscription.isActiveTrial()
                        && recruitmentRepository.countByRecruitmentProfileJobRecruiter(recruiter) >= 1
        ) {
            throw new TrialQuotaExceeded();
        }
    }

    private static void updateJobTitle(String title, Job job) {
        if (StringUtils.isNotBlank(title)) {
            job.setTitle(title);
        }
    }

    private Recruitment saveRecruitment(Recruitment recruitment) {
        var savedRecruitment = recruitmentRepository.save(recruitment);
        savedRecruitment.updateCodeOnJobCreate();
        return savedRecruitment;
    }

    private static void updateJobAndRecruitmentLocation(LocationDTO locationDTO, Job job, Recruitment recruitment) {
        job.setLocation(Location.buildLocationFromDTO(locationDTO));
        recruitment.setLocation(Location.buildLocationFromDTO(locationDTO));
    }

    private void sendRecruitmentNotification(Recruiter organization, String title, Recruitment recruitment, ErhgoOccupation occupation) {
        var titleUsed = occupation.getTitle().equals(title) ? " en utilisant son titre principal " : " en utilisant le libellé '%s' pour le métier '%s'".formatted(title, occupation.getTitle());
        recruitment.refreshClassificationsUsingOccupation();
        var isSubscriptionActivated = sourcingService.isRecruiterSubscriptionActivated(organization);
        notifier.sendMessage(new SourcingJobOnOccupationMessage(occupation, organization, recruitment.getCode(), titleUsed, isSubscriptionActivated));
    }

    private Recruitment createNewSourcingRecruitment(Job job) {
        var recruitmentProfile = recruitmentProfileRepository.save(RecruitmentProfile.buildWithAllOptional(job, null));
        return Recruitment.builder()
                .recruitmentProfile(recruitmentProfile)
                .title(buildRecruitmentTitle(job.getTitle()))
                .state(RecruitmentState.DRAFT)
                .sourcingStep(0)
                .sourcingUsersIdToNotify(Set.of(securityService.getAuthenticatedUserId()))
                .managerUserId(securityService.getAuthenticatedUserId())
                .build();
    }

    private static String buildRecruitmentTitle(String title) {
        return "Recrutement pour : %s".formatted(title);
    }

    @Transactional
    public void suspendSourcingRecruitmentsOnDisabledAccount() {
        var disabledRecruiters = sourcingSubscriptionRepository
                .findByExpirationDateBeforeAndInvitationIsNull(OffsetDateTime.now().minusDays(1))
                .stream()
                .map(SourcingSubscription::getRecruiter)
                .toList();
        var recruitmentsToSuspend = recruitmentRepository.findByExternalOfferIsNullAndRecruitmentProfileJobRecruiterInAndState(disabledRecruiters, RecruitmentState.PUBLISHED);
        if (!recruitmentsToSuspend.isEmpty()) {
            recruitmentsToSuspend.forEach(r -> r.setState(RecruitmentState.SELECTION, true));
            log.warn("{} recruitments suspended due to disabled recruiters : {}", recruitmentsToSuspend.size(), recruitmentsToSuspend.stream().map(Recruitment::getRecruiter).map(r -> "%s (%s)".formatted(r.getTitle(), r.getCode())));
            notifier.sendMessage(new SourcingDisabledAccountMessageDTO(recruitmentsToSuspend));
        } else {
            log.debug("{} subscriptions expired, no recruitment", disabledRecruiters.size());
        }
    }

    @Transactional
    public void suspendEndedSourcingRecruitments() {
        var recruitmentsToSuspend = recruitmentRepository.findByPublicationEndDateBeforeAndState(OffsetDateTime.now(), RecruitmentState.PUBLISHED);
        if (!recruitmentsToSuspend.isEmpty()) {
            recruitmentsToSuspend.forEach(r -> r.setState(RecruitmentState.SELECTION, true));
            recruitmentsToSuspend.forEach(sourcingMailingService::sendEmailAboutRecruitmentSuspensionAfterEndDate);
            log.info("{} recruitments suspended (ids={})", recruitmentsToSuspend.size(), recruitmentsToSuspend.stream().map(r -> "%d".formatted(r.getId())).collect(Collectors.joining(", ")));
        } else {
            log.debug("No recruitment to suspend");
        }
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_WRITE)
    public List<Long> changeRecruitmentStateForBatch(Long recruitmentId,
                                                     ChangeSourcingRecruitmentStateCommandDTO command) {
        var recruitment = recruitmentRepository.findById(recruitmentId).orElseThrow().getRecruiter();
        return changeRecruitmentStateInternal(
                recruitmentId,
                command,
                () -> sourcingSubscriptionRepository.findOneByRecruiter(recruitment).orElse(null),
                true
        );
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_WRITE)
    public List<Long> changeRecruitmentState(Long recruitmentId, ChangeSourcingRecruitmentStateCommandDTO command) {
        return changeRecruitmentStateInternal(recruitmentId, command, sourcingService::getAuthenticatedUserSubscription, false);
    }

    private List<Long> changeRecruitmentStateInternal(
            Long recruitmentId,
            ChangeSourcingRecruitmentStateCommandDTO command,
            Supplier<SourcingSubscription> subscriptionSupplier,
            boolean allowRepublicationOfAlreadyPublishedRecruitment) {
        var recruitment = getRecruitmentOrThrow(recruitmentId);
        var result = new ArrayList<Long>();
        if (StringUtils.isNotBlank(command.getNewTitle())) {
            recruitment.setJobTitle(command.getNewTitle());
        }
        if (command.getNextState() == RecruitmentStateDTO.PUBLISHED) {
            if (!allowRepublicationOfAlreadyPublishedRecruitment && recruitment.getState() == RecruitmentState.PUBLISHED) {
                log.debug("Ignoring re-publication of already published recruitment");
                return Collections.emptyList();
            }
            var republication = recruitment.getState() != RecruitmentState.DRAFT;
            var subscription = subscriptionSupplier.get();
            recruitment.publishSourcingRecruitment(
                    subscription,
                    trialDurationInDays,
                    !republication && command.getSendNotifications() != UsersToNotifySelectionTypeDTO.NONE,
                    recruitmentLifespanInDays,
                    allowRepublicationOfAlreadyPublishedRecruitment);
            notifyPublishedRecruitment(subscription, recruitment, republication, command.getSendNotifications());
            result.addAll(notifyAndGenerateCandidaturesIfRequired(command, recruitment, republication));
        } else {
            var wasClosed = recruitment.isClosed();
            recruitment.setState(RecruitmentState.valueOf(command.getNextState().name()), false);
            if (command.getNextState() == RecruitmentStateDTO.CLOSED && !wasClosed) {
                closedRecruitmentMailService.notifyRecruitmentClosed(recruitment);
            }
        }
        return result;
    }

    private List<Long> notifyAndGenerateCandidaturesIfRequired(ChangeSourcingRecruitmentStateCommandDTO command, Recruitment recruitment, boolean republication) {
        var shouldNotify = command.getSendNotifications() != UsersToNotifySelectionTypeDTO.NONE;
        if (republication && shouldNotify) {
            inviteToRecruitmentNow(command, recruitment);
        }
        return shouldNotify ? sourcingService.regenerateTopTenCandidatures(recruitment) : Collections.emptyList();
    }

    private void inviteToRecruitmentNow(ChangeSourcingRecruitmentStateCommandDTO command, Recruitment recruitment) {
        var invitationCommand = new InviteToRecruitmentCommandDTO()
                .recruitmentId(recruitment.getId())
                .forceResend(command.getSendNotifications() == UsersToNotifySelectionTypeDTO.ALL);
        sourcingService.generateRecruitmentNotifications(invitationCommand);
    }

    private void notifyPublishedRecruitment(SourcingSubscription subscription, Recruitment recruitment, boolean republication, UsersToNotifySelectionTypeDTO sendNotifications) {
        var isSubscriptionActivated = Optional.ofNullable(subscription).map(SourcingSubscription::isActivated).orElse(false);
        var notifiedUserIds = sourcingUserRepository.getCandidates(sourcingCandidateCriteriaBuilder.forRecruitment(recruitment, false)
                .excludesNotified(sendNotifications != UsersToNotifySelectionTypeDTO.ALL));
        var mobileNotifiedUserIds = userMobileTokenRepository.findByUserProfileUserIdIn(notifiedUserIds).stream().map(t -> t.getUserProfile().userId()).distinct().count();
        var message = SourcingPublishRecruitmentMessageDTO.builder()
                .recruiter(recruitment.getRecruiterTitle())
                .jobTitle(recruitment.getJobTitle())
                .isSubscriptionActivated(securityService.isAdmin() || isSubscriptionActivated)
                .location(recruitment.getLocation())
                .sendMailDate(recruitment.getSendNotificationDate())
                .numberOfTotalNotification(notifiedUserIds.size())
                .numberOfMobilesNotification((int) mobileNotifiedUserIds)
                .republication(republication)
                .sendMailRequired(sendNotifications)
                .build();
        notifier.sendMessage(message);
    }


    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_WRITE)
    public void updateStep(Long recruitmentId, UpdateSourcingStepCommandDTO command) {
        recruitmentRepository.findById(recruitmentId).orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class)).setSourcingStep(command.getStep());
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_WRITE)
    public void updateQuestionOnRecruitment(Long recruitmentId, UpdateSourcingRecruitmentQuestionCommandDTO command) {
        var recruitment = getRecruitmentOrThrow(recruitmentId);
        recruitment.getRecruitmentProfile().setCustomQuestion(command.getQuestion());
    }
}
