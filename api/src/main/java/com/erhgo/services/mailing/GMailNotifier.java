package com.erhgo.services.mailing;

import com.erhgo.config.EnvironmentProfile;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.userprofile.FilePartProvider;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.gmail.model.Message;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.common.base.Joiner;
import jakarta.annotation.PostConstruct;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import jakarta.validation.constraints.Null;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.*;
import java.util.stream.Stream;

import static jakarta.mail.Message.RecipientType.TO;

@Service
@ConditionalOnExpression("!'${gmail.apiKey}'.isEmpty()")
@Slf4j
@RequiredArgsConstructor
public class GMailNotifier implements MailNotifier {

    private Gmail gmailService;
    @Value("${gmail.senderEmail}")
    private String senderEmail;
    @Value("${gmail.applicationName}")
    private String applicationName;
    @Value("${gmail.apiKey}")
    private String apiKey;

    private final Environment environment;

    @PostConstruct
    public void init() throws GeneralSecurityException, IOException {
        var credentials = ServiceAccountCredentials.fromStream(
                        new ByteArrayInputStream(apiKey.getBytes(StandardCharsets.UTF_8))
                )
                .createScoped(Collections.singleton(GmailScopes.GMAIL_SEND))
                .createDelegated(senderEmail);

        gmailService = new Gmail.Builder(
                GoogleNetHttpTransport.newTrustedTransport(),
                GsonFactory.getDefaultInstance(),
                new HttpCredentialsAdapter(credentials)
        ).setApplicationName(applicationName)
                .build();
        log.info("Service DirectMailNotifier initialized correctly");
    }

    private MimeMessage createMessage(Collection<String> to, String replyToEmail, String replyToAlias, boolean useReplyToAliasAsFrom, String subject, String body, FilePartProvider... parts) throws MessagingException {
        var email = new MimeMessage(Session.getDefaultInstance(System.getProperties(), null));
        try {
            email.setFrom(new InternetAddress(senderEmail, useReplyToAliasAsFrom ? replyToAlias : null));
            email.setReplyTo(new InternetAddress[]{new InternetAddress(replyToEmail, replyToAlias)});
            email.addRecipients(TO, Joiner.on(",").join(to));
            email.setSubject(subject);
            var textBodyPart = new MimeBodyPart();
            textBodyPart.setText(body, "utf-8");
            var multipart = new MimeMultipart();
            multipart.addBodyPart(textBodyPart);
            Stream.of(parts).filter(FilePartProvider::isValid)
                    .forEach((FilePartProvider p) -> {
                        var attachmentBodyPart = new MimeBodyPart();
                        try {
                            attachmentBodyPart.setFileName(p.fileName());
                            attachmentBodyPart.setContent(p.readAllBytes(), p.contentType());
                            multipart.addBodyPart(attachmentBodyPart);
                        } catch (IOException | MessagingException e) {
                            log.error("Unable to add part {} to ATS email - recipîent {}, subject {}, candidate {}", p.fileName(), to, subject, replyToEmail);
                        }
                    });
            email.setContent(multipart);
            return email;
        } catch (UnsupportedEncodingException e) {
            throw new GenericTechnicalException("unable to set addresses", e);
        }
    }

    private void sendMessage(MimeMessage email) throws MessagingException, IOException {
        var buffer = new ByteArrayOutputStream();
        email.writeTo(buffer);
        var rawMessageBytes = buffer.toByteArray();
        var encodedEmail = Base64.getUrlEncoder().encodeToString(rawMessageBytes);
        var message = new Message();
        message.setRaw(encodedEmail);
        var result = gmailService.users().messages().send(senderEmail, message).execute();
        log.trace("mail sent for userId {}, subject {} and recipient {}: {}", senderEmail, email.getSubject(), email.getRecipients(TO), result.toPrettyString());
    }

    public void sendMail(Collection<String> to, String replyTo, String replyToAlias, boolean useReplyToAsFrom, String rawSubject, String body, @Null FilePartProvider... parts) {
        try {
            var subject = Arrays.stream(environment.getActiveProfiles())
                    .map(EnvironmentProfile::fromText)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .findFirst()
                    .map(EnvironmentProfile::getText)
                    .map(EnvironmentProfile::formatProfileForEmailSubject)
                    .map(envPrefix ->
                            "%s (%s)".formatted(envPrefix, rawSubject)
                    )
                    .orElse(rawSubject);

            log.trace("Sending gmail email - recipient {}, rawSubject {}, subject {}", to, rawSubject, subject);
            var message = createMessage(to, replyTo, replyToAlias, useReplyToAsFrom, subject, body, parts);
            sendMessage(message);
            log.debug("Gmail Email sent - recipient {}, rawSubject {}, subject {}", to, rawSubject, subject);
        } catch (MessagingException | IOException e) {
            log.error("Unable to send email with subject {} (email {}, {} file parts)", rawSubject, to, parts.length, e);
            throw new GenericTechnicalException("Unable to send email", e);
        }
    }
}
