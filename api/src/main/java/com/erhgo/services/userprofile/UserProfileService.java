package com.erhgo.services.userprofile;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.dto.EmailVerificationResultDTO;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidJobTitleException;
import com.erhgo.domain.exceptions.UserConflictException;
import com.erhgo.domain.exceptions.UserNotAllowedForEntity;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.landingpage.LandingPage;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.*;
import com.erhgo.domain.userprofile.event.UserExperienceUpdatedEvent;
import com.erhgo.domain.userprofile.event.UserJobOffersOptInUpdatedEvent;
import com.erhgo.domain.userprofile.event.UserLastConnectionEvent;
import com.erhgo.domain.userprofile.event.UserNameUpdatedEvent;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.dto.UserMatchingSummary;
import com.erhgo.repositories.dto.UserProfileProgress;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.ChannelAssignationHelper;
import com.erhgo.services.RecruitmentHelper;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import com.erhgo.services.dto.criteria.MatchingJobsCriteria;
import com.erhgo.services.dtobuilder.*;
import com.erhgo.services.generation.OccupationForLabelGenerationService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.mailing.SendEmailToConfirmUserFromBOService;
import com.erhgo.services.mailing.check.EmailVerificationService;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.utils.DateTimeUtils;
import com.erhgo.utils.RefererUtils;
import com.erhgo.utils.StringUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.opencsv.CSVWriter;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.opencsv.ICSVWriter.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserProfileService {
    private final NotificationRepository notificationRepository;

    private static final String UNDEFINED_LABEL = "(Non défini)";
    private static final int USERS_MATCHING_JOB_EMAIL_INDEX = 3;
    private static final int USERS_MATCHING_JOB_PHONE_INDEX = 6;
    private static final int USERS_MATCHING_JOB_NAME_INDEX = 2;
    private static final int USER_PROFILE_COMPLETION_BASE_PERCENTAGE = 50;
    private static final int USER_PROFILE_COMPLETION_INCOMPLETE_INFOS_MAX = 10;

    private final CriteriaRepository criteriaRepository;
    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final JobRepository jobRepository;
    private final LandingPageRepository landingPageRepository;
    private final AbstractOrganizationRepository organizationRepository;
    private final RecruiterRepository recruiterRepository;
    private final RecruitmentRepository recruitmentRepository;
    private final UserExperienceRepository userExperienceRepository;
    private final UserProfileRepository userProfileRepository;
    private final GeneralInformationRepository generalInformationRepository;
    private final ConfigurablePropertyRepository configurablePropertyRepository;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    private final KeycloakService keycloakService;
    private final SecurityService securityService;
    private final OccupationForLabelGenerationService erhgoOccupationImporterService;

    private final CriteriaDTOBuilder criteriaDTOBuilder;
    private final GeneralInformationDTOBuilder generalInformationDTOBuilder;
    private final UserProfileDTOBuilder userProfileDTOBuilder;
    private final UserSummaryDTOBuilder userSummaryDTOBuilder;

    private final ChannelAssignationHelper channelAssignationHelper;
    private final RecruitmentHelper recruitmentHelper;
    private final TransactionTemplate transactionTemplate;
    private final UserProfileProvider userProfileProvider;
    private final EmailVerificationService emailVerificationService;

    private final SendEmailToConfirmUserFromBOService sendEmailToConfirmUserFromBoService;
    private final BehaviorRepository behaviorRepository;
    private final MailingListService mailingListService;

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public String createUser(CreateUserCommandDTO command) {
        var keycloakRepresentation = UserKeycloakRepresentation.createAdmin(
                command.getFirstName(),
                command.getLastName(),
                command.getEmail(),
                command.getPassword());
        return keycloakService.createUserInBackOfficeRealm(keycloakRepresentation);
    }


    @RolesAllowed(Role.ODAS_ADMIN)
    public List<UserSummaryDTO> getMembersOfGroups(String organizationCode) {
        Set<String> group = keycloakService.getGroupsOfRoles(Collections.singleton(organizationCode));
        return group.stream().flatMap(g -> keycloakService.getBackOfficeGroupMembersPaginatedResource(g, 0, 10000)
                        .getContent().stream()
                        .filter(UserRepresentation::getEnabled)
                        .map(userSummaryDTOBuilder::buildUserSummary))
                .sorted(Comparator.comparing(a -> Stream.of(a.getLastName(), a.getFirstName(), a.getEmail()).filter(Objects::nonNull).findFirst().orElse(""), StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE)).toList();
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public CreateUserFOResultDTO createUserFO(CreateUserFOCommandDTO command) {

        ensureNoUserWithPhoneNumber(command.getPhoneNumber());

        var keycloakRepresentation = new UserKeycloakRepresentation()
                .setFirstName(command.getFirstName())
                .setEmail(computeTmpEmail(command))
                .setLastName(command.getLastName());
        var userId = keycloakService.createUserInFrontOfficeRealm(keycloakRepresentation);
        var userProfile = userProfileProvider.getUserProfileOrCreateSilently(userId);
        userProfile.generalInformation().setPhoneNumber(command.getPhoneNumber());
        userProfile.markAsCreatedFromBO();
        return new CreateUserFOResultDTO().userId(userId);
    }

    private void ensureNoUserWithPhoneNumber(@NotNull String phoneNumber) {
        var normalizedPhone = StringUtils.normalizePhone(phoneNumber);
        if (normalizedPhone == null) {
            return;
        }
        var phoneNumberPatterns = Stream.of(
                phoneNumber,
                normalizedPhone,
                normalizedPhone.replace("+", "00"),
                normalizedPhone.replace("+", ""),
                normalizedPhone.replaceFirst("0033", "0"),
                normalizedPhone.replaceFirst("00", "")
        ).collect(Collectors.toSet());
        var usersWithPhone = generalInformationRepository.findByPhoneNumber(phoneNumberPatterns);
        if (!usersWithPhone.isEmpty()) {
            throw new UserConflictException("phoneNumber", GeneralInformation.class, phoneNumber, usersWithPhone);
        }

    }

    private String computeTmpEmail(CreateUserFOCommandDTO command) {
        return Optional.ofNullable(command.getEmail())
                .orElseGet(() -> StringUtils.getTemporaryEmail(command.getPhoneNumber()));
    }


    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void setFrontOfficeUserPassword(SetFrontOfficeUserPasswordCommandDTO setFrontOfficeUserPasswordCommandDTO) {
        keycloakService.setFrontOfficeUserPassword(setFrontOfficeUserPasswordCommandDTO);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void confirmFOUserFromBO(ConfirmFOUserFromBOCommandDTO confirmFOUserFromBOCommandDTO) {
        var pwd = keycloakService.confirmFOUserFromBO(confirmFOUserFromBOCommandDTO);
        sendEmailToConfirmUserFromBoService.sendEmailForNewUserBO(confirmFOUserFromBOCommandDTO.getUserId(), pwd);

        var userProfile = userProfileProvider.getUserProfileOrCreateSilently(confirmFOUserFromBOCommandDTO.getUserId());
        userProfile.userRegistrationState().updateRegistrationStep(UserRegistrationState.RegistrationStep.BO_CONFIRMED);
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public UserProfileSummaryDTO findCandidateUserProfileByUserId(String userId) {
        var userProfile = userProfileRepository.findByUserId(userId)
                .orElseGet(userProfileProvider::getAuthenticatedUserProfileOrCreate);
        return userProfileDTOBuilder.buildSummary(userProfile);

    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public UserProfileDetailWithCapacitiesDTO getUserProfileDetailsWithCapacities(String userId) {
        var userProfile = getUserProfileOrThrow(userId);
        var keycloakUserWithRestrictedGroups = keycloakService.getFrontOfficeUserProfile(userId).orElseThrow();
        keycloakUserWithRestrictedGroups.setGroups(List.copyOf(securityService.intersectGroupsWithAuthenticatedUserAuthorizedGroups(Lists.newArrayList(userProfile.channels()))));

        return userProfileDTOBuilder.buildDetailedWithCapacities(userProfile, keycloakUserWithRestrictedGroups);
    }

    private UserProfile getUserProfileOrThrow(String userId) {
        return userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public List<UserCriteriaValueDTO> getUserCriterias(String userId) {
        return criteriaRepository.findUserCriteriaValuesByUserId(userId).stream()
                .map(criteriaDTOBuilder::buildCriteriaSummaryDTO)
                .toList();
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public List<ExperienceDetailsDTO> getUserExperiences(String userId) {
        return userExperienceRepository.findByUserProfileUserId(userId).stream()
                .sorted(Collections.reverseOrder())
                .map(UserExperienceDTOBuilder::buildExperienceDetailsDTO)
                .toList();
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public CapacitiesResultDTO getUserCapacities(String userId) {
        var userProfile = getUserProfileOrThrow(userId);
        var occurrences = CapacityDTOBuilder.buildCapacityAndLevelDTOSForRepositoryDTOS(userProfile.getAllCapacities(), userProfile.buildLevelForCapacityMap());
        return new CapacitiesResultDTO().capacities(occurrences);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public float getUserLevel(String userId) {
        return getUserProfileOrThrow(userId).masteryLevel();
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public InitializedProfileDTO initializeProfile(InitializeProfileCommandDTO command) {
        return initializeProfile(command, null);
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public InitializedProfileDTO initializeProfile(InitializeProfileCommandDTO command, String referer) {
        var userProfile = userProfileProvider.getAuthenticatedUserProfileOrCreate();
        var newChannels = getRecruiters(command);
        var source = UserChannel.ChannelSourceType.valueOf(command.getChannelSource().name());
        userProfile.markNewConnection();
        channelAssignationHelper.assignOnProfileInitialization(newChannels, source, userProfile);

        if (RefererUtils.isHandicap(referer)) {
            userProfile.isFromHandicap(true);
            userProfile.handicapModeEnabled(true);
        }

        var organizations = organizationRepository.findByCodeIn(userProfile.channels());
        var mandatoryIdentity = organizations.stream().anyMatch(AbstractOrganization::isMandatoryIdentity);

        var result = new InitializedProfileDTO();
        if (userProfile.requiresVerificationNow()) {
            result.correctEmailSuggestion(processVerification(userProfile, getEmailOrThrow(userProfile)).getSuggestion());
        }

        EventPublisherUtils.publish(new UserLastConnectionEvent(userProfile));
        return result
                .registrationStep(UserRegistrationStateStepDTO.fromValue(userProfile.userRegistrationState().getRegistrationStep().name()))
                .mandatoryIdentity(mandatoryIdentity)
                .confirmationRequired(userProfile.isEmailConfirmationRequired())
                .shouldAskForMailOptIn(userProfile.shouldAskForMailOptIn())
                ;
    }

    private EmailVerificationResultDTO processVerification(UserProfile userProfile, String email) {
        var verificationResult = emailVerificationService.verify(email);
        userProfile.updateMailVerificationStateWith(verificationResult);
        return verificationResult;
    }

    @org.jetbrains.annotations.NotNull
    private String getEmailOrThrow(UserProfile userProfile) {
        return keycloakService.getFrontOfficeUserProfile(userProfile.userId()).map(UserRepresentation::getEmail).orElseThrow();
    }


    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void updateRegistrationState(UpdateRegistrationStepCommandDTO step) {
        var userProfile = getUserProfileOrThrow(securityService.getAuthenticatedUserId());
        var userStateStep = UserRegistrationState.RegistrationStep.valueOf(step.getValue().name());
        userProfile.updateRegistrationState(userStateStep);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void updateUsersChannels(UpdateUsersChannelsCommandDTO command) {
        var allRelatedChannels = Lists.newArrayList(Optional.ofNullable(command.getChannelsToAdd()).orElse(Collections.emptyList()));
        allRelatedChannels.addAll(Optional.ofNullable(command.getChannelsToRemove()).orElse(Collections.emptyList()));

        if (!securityService.isAdmin() && !securityService.hasAllRoles(allRelatedChannels.toArray(String[]::new))) {
            throw new UserNotAllowedForEntity("Some channels are not accessible to authenticated user");
        }
        if (CollectionUtils.isNotEmpty(command.getChannelsToRemove())) {
            command.getUsersId().forEach(userId -> this.removeUserFromChannels(userId, command.getChannelsToRemove()));
            log.info("Users {} were removed from channels {}", command.getUsersId(), command.getChannelsToRemove());
        }

        if (CollectionUtils.isNotEmpty(command.getChannelsToAdd())) {
            command.getUsersId().forEach(userId -> this.addUserToChannels(userId, command.getChannelsToAdd()));
            log.info("Users {} were added to channels {}", command.getUsersId(), command.getChannelsToAdd());
        }
    }

    private void removeUserFromChannels(String userId, Collection<String> channels) {
        channels.forEach(c -> keycloakService.removeUserFromFrontOfficeGroup(userId, c));
        transactionTemplate.execute(z -> {
            userProfileRepository.findByUserId(userId).ifPresent(up -> up.removeFromChannels(channels));
            return null;
        });
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void addUserToChannels(String userId, Collection<String> channels) {
        channels.forEach(c -> keycloakService.assignToFrontOfficeGroups(userId, Set.copyOf(channels)));
        transactionTemplate.execute(z -> {
            userProfileRepository.findByUserId(userId).ifPresent(up -> up.updatedChannels(Set.copyOf(channels), UserChannel.ChannelSourceType.ADMIN));
            return null;
        });
    }

    private Set<Recruiter> getRecruiters(InitializeProfileCommandDTO command) {
        Set<Recruiter> organizations = Sets.newHashSet();
        if (command.getValue() != null) {
            if (command.getChannelSource() == UserChannelSourceDTO.LANDING_PAGE) {
                var landing = landingPageRepository
                        .getByUrlKeyIn(command.getValue());
                organizations.addAll(landing.stream().map(LandingPage::getOrganizations).flatMap(Collection::stream).collect(Collectors.toSet()));
            } else {
                var recruiters = recruiterRepository.findByCodeIn(command.getValue());
                organizations.addAll(recruiters);
            }
        }
        return organizations;
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public UserMatchingJobPageDTO getUsersMatchingJob(UUID jobId, MatchingJobsCriteria page) {
        log.debug("Starting getUsersMatchingJob...");
        if (!securityService.isAdmin() && (page.getOrganizationCodes() == null || !page.getOrganizationCodes().stream().allMatch(securityService::hasAuthenticatedUserRole))) {
            throw new UserNotAllowedForEntity("Authenticated user does not have access to all organization roles (at least one must be provided)");
        }

        var job = jobRepository.findById(jobId).orElseThrow(() -> new EntityNotFoundException(jobId, Job.class));
        var keycloakUsers = getUsersMatchingCapacitiesForGroups(job, page);

        log.debug("About to compute progress for {} users", keycloakUsers.getContent().size());
        var progressDtos = getUserProgressByUserId(keycloakUsers.getContent(), page.getOrganizationCodes());
        log.info("Progress computed finished for {} users", keycloakUsers.getContent().size());

        var recruitmentIds = recruitmentRepository.findPublishedByJobId(job.getId()).stream().
                map(Recruitment::getId).collect(Collectors.toSet());

        var candidatures = recruitmentCandidatureRepository.findDistinctByRecruitmentRecruitmentProfileJobId(jobId);

        keycloakUsers.forEach(x -> {
            var userProfileProgress = progressDtos.get(x.getId());
            x.setUserProfileProgress(UserProfileDTOBuilder.buildUserProfileProgress(userProfileProgress));

            var userProfile = userProfileProgress.getUserProfile();
            var anyRefusedCandidature = anyRefusedCandidature(userProfile, candidatures);
            x.setAnyRefusedCandidature(anyRefusedCandidature);
            var notifiedRecruitments = notificationRepository.findRecruitmentNotificationByUserProfileUuid(userProfile.uuid());
            x.setAlreadyReceivedProposalEmail(
                    notifiedRecruitments
                            .stream()
                            .anyMatch(y -> recruitmentIds.contains(y.getRecruitment().getId()))
            );
        });
        log.info("getUsersMatchingJob ended");

        return PageDTOBuilder.buildUserMatchingJobPage(keycloakUsers);
    }

    private boolean anyRefusedCandidature(UserProfile userProfile, List<RecruitmentCandidature> allCandidatures) {
        return allCandidatures.stream()
                .filter(c -> c.getUserProfile().userId().equals(userProfile.userId()))
                .anyMatch(RecruitmentCandidature::isRefused);
    }

    private Page<MatchingUserSummaryDTO> getUsersMatchingCapacitiesForGroups(Job job, MatchingJobsCriteria page) {
        var capacities = job.getAllCapacities().stream().map(Capacity::getId).collect(Collectors.toSet());
        var numberOfCapacitiesInJob = capacities.size();
        // using ordinal for native query
        var minLevel = job.getMasteryLevel() == null ? 0 : job.getMasteryLevel().ordinal();
        // recruitments required to know whether user already has candidature or not
        var recruitmentIds = recruitmentHelper.findRecruitments(job.getId())
                .stream()
                .map(Recruitment::getId)
                .collect(Collectors.toSet());
        var masteryLevelThreshold = Float.parseFloat(configurablePropertyRepository.findOneByPropertyKey("user_mastery_level_threshold").getPropertyValue());

        var criteriaBuilder = UserMatchingCapacitiesCriteria
                .builder()
                .capacitiesIds(capacities)
                .capacitiesLength(numberOfCapacitiesInJob)
                .capacityThreshold((int) (page.getCapacityThreshold() == null ? 0 : page.getCapacityThreshold() * 100))
                .minLevel(minLevel)
                .recruitmentIds(recruitmentIds)
                .masteryLevelRange(page.getMasteryLevelRange() == null ? masteryLevelThreshold : page.getMasteryLevelRange())
                .limit(page.getPageSize())
                .postcode(page.getPostcode())
                .offset(page.getPageNumber() * (long) page.getPageSize())
                .criteriaValueCodes(page.getCriteriaCodes())
                // Next line to exclude users associated to private channel, except this job's channel
                .excludedChannels(getAllPrivateChannelsExcludingJobsOne(job));
        var organizationRoles = handleChannelsOnCriteriaBuilderAndRetrieveOrganizationRoles(
                criteriaBuilder,
                page.getOrganizationCodes(),
                page.getStrictOrganizationFilter(),
                BooleanUtils.isTrue(page.getIsAffectedToNoChannel()));

        var criteria = criteriaBuilder.build();

        var count = userProfileRepository.countUserMatchingCapacities(criteria);

        var users = count > 0 ? userProfileRepository.getUserMatchingCapacities(criteria) : Collections.<UserMatchingSummary>emptyList();

        var userList = users
                .stream()
                .map(userMatchingSummary -> generalInformationDTOBuilder.buildMatchingUserSummary(
                        userMatchingSummary,
                        getKeycloakUserRepresentationWithFilteredGroups(userMatchingSummary, organizationRoles),
                        numberOfCapacitiesInJob,
                        page.getCriteriaCodes() != null ? page.getCriteriaCodes() : Collections.emptyList()
                ))
                .toList();
        return new PageImpl<>(userList, PageRequest.of(page.getPageNumber(), page.getPageSize()), count);
    }

    private Set<String> getAllPrivateChannelsExcludingJobsOne(Job job) {
        return organizationRepository
                .findByPrivateUsers(true)
                .stream()
                .map(AbstractOrganization::getCode)
                .filter(c -> !c.equals(job.getRecruiterCode()))
                .collect(Collectors.toSet());
    }


    private Optional<UserRepresentation> getKeycloakUserRepresentationWithFilteredGroups(UserMatchingSummary userMatchingSummary, Set<String> organizationRoles) {
        var keycloakUserOpt = keycloakService.getFrontOfficeUserProfile(userMatchingSummary.getUserId());

        keycloakUserOpt.ifPresent(keycloakUser -> {
            var dbUserChannels = userMatchingSummary.getChannels();
            if (dbUserChannels != null) {
                if (organizationRoles != null) {
                    keycloakUser.setGroups(Lists.newArrayList(Sets.intersection(dbUserChannels, organizationRoles)));
                } else {
                    keycloakUser.setGroups(Lists.newArrayList(dbUserChannels));
                }
            }
        });
        return keycloakUserOpt;
    }

    private Set<String> handleChannelsOnCriteriaBuilderAndRetrieveOrganizationRoles(
            UserMatchingCapacitiesCriteria.UserMatchingCapacitiesCriteriaBuilder criteria,
            Collection<String> organizationCodes,
            Boolean strictOrganizationFilter,
            boolean isAffectedToNoChannel) {
        @SuppressWarnings("java:S1854")
        var ignoreChannels = organizationCodes == null;
        Set<String> effectiveOrganizationCodes = null;
        if (!ignoreChannels) {
            if (BooleanUtils.isTrue(strictOrganizationFilter)) {
                effectiveOrganizationCodes = Set.copyOf(organizationCodes);
            } else {
                effectiveOrganizationCodes = organizationCodes.stream().flatMap(x -> keycloakService.getRolesForGroup(x).stream()).collect(Collectors.toSet());
            }
        }
        criteria
                .channels(ignoreChannels || effectiveOrganizationCodes.isEmpty() ? Sets.newHashSet("NO_ORGA") : effectiveOrganizationCodes)
                .noChannel(ignoreChannels && !isAffectedToNoChannel)
                .affectedToNoChannel(isAffectedToNoChannel);

        return effectiveOrganizationCodes;
    }

    private Map<String, UserProfileProgress> getUserProgressByUserId(List<MatchingUserSummaryDTO> keycloakUsers, Collection<String> organizationCodes) {
        var usersIds = keycloakUsers.stream()
                .map(MatchingUserSummaryDTO::getId)
                .collect(Collectors.toSet());
        return userProfileRepository
                .getUserProfilesProgress(
                        usersIds,
                        organizationCodes,
                        securityService.isAdmin(),
                        false,
                        null)
                .stream()
                .collect(Collectors.toMap(UserProfileProgress::getUserId, Function.identity()));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public void writeUsersMatchingJobCsv(OutputStreamWriter writer, UUID jobId, MatchingJobsCriteria page) {
        var organizationTitleForCode = StreamSupport.stream(recruiterRepository.findAll(Pageable.unpaged()).spliterator(), false)
                .collect(Collectors.toMap(Recruiter::getCode, Recruiter::getTitle));

        var usersMatchingJob = getUsersMatchingJob(jobId, page);

        var job = jobRepository.findById(jobId).orElseThrow(() -> new EntityNotFoundException(jobId, Job.class));
        var employerName = getEmployerName(job);

        var usersMatchingJobStream = usersMatchingJob.getContent().stream()
                .sorted(Comparator.comparing(MatchingUserSummaryDTO::getMatchingRateInPercent).reversed()
                        .thenComparing(x -> x.getContactInformation().getEmail())
                        .thenComparing(MatchingUserSummaryDTO::getId))
                .map(user -> buildUserMatchingJobExportLine(user, organizationTitleForCode, employerName, job.getTitle()));

        var headers = new ArrayList<String>(List.of("Date de création", "Prénom", "Ville", "Code postal", "Situation", "Salaire", "Taux complétude", "Nombre de candidatures", "Organisation", "Correspondance", "Poste", "Employeur"));
        headers.add(USERS_MATCHING_JOB_NAME_INDEX, "Nom");
        if (!isAnonymizationRequired()) {
            headers.add(USERS_MATCHING_JOB_EMAIL_INDEX, "Adresse mail");
            headers.add(USERS_MATCHING_JOB_PHONE_INDEX, "Numéro de téléphone");
        }

        try (var csvWriter = new CSVWriter(
                writer,
                StringUtils.CSV_FIELD_SEPARATOR,
                DEFAULT_QUOTE_CHARACTER,
                DEFAULT_ESCAPE_CHARACTER,
                DEFAULT_LINE_END)) {
            csvWriter.writeNext(headers.toArray(String[]::new));
            csvWriter.writeAll(usersMatchingJobStream::iterator);
        } catch (IOException e) {
            log.error("Unable to generate CSV", e);
        }
    }

    private boolean isAnonymizationRequired() {
        return !securityService.hasAuthenticatedUserAnyRole(Role.ODAS_ADMIN);
    }

    private String getEmployerName(Job job) {
        return Optional.ofNullable(job.getEmployerCode())
                .map(organizationRepository::findOneByCode)
                .map(AbstractOrganization::getTitle)
                .orElse(UNDEFINED_LABEL);
    }

    private String[] buildUserMatchingJobExportLine(
            MatchingUserSummaryDTO matchingUserSummary,
            Map<String, String> organizationTitleForCode,
            String employerName,
            String jobTitle) {
        var contactInformation = matchingUserSummary.getContactInformation();
        var location = contactInformation.getLocation();
        var userProfileProgress = matchingUserSummary.getUserProfileProgress();

        var values = Lists.newArrayList(
                formatTimestamp(contactInformation.getCreationDate()),
                contactInformation.getFirstName(),
                formatString(location.getCity()),
                formatString(location.getPostcode()),
                formatSituation(contactInformation.getSituation()),
                formatSalary(contactInformation.getSalary()),
                formatUserProgressProfile(userProfileProgress),
                userProfileProgress.getCandidaturesCount().toString(),
                joinOrganizationTitles(organizationTitleForCode, contactInformation.getChannels()),
                formatMatchingRatePercent(matchingUserSummary.getMatchingRateInPercent()),
                jobTitle,
                employerName);
        if (isAnonymizationRequired()) {
            values.add(USERS_MATCHING_JOB_NAME_INDEX, StringUtils.anonymize(contactInformation.getLastName()));
        } else {
            values.add(USERS_MATCHING_JOB_NAME_INDEX, contactInformation.getLastName());
            values.add(USERS_MATCHING_JOB_EMAIL_INDEX, BooleanUtils.isTrue(contactInformation.getTransactionalBlacklisted()) ? "(Désabonné)" : contactInformation.getEmail());
            var phoneLabel = StringUtils.getPhoneLabel(securityService.isAdminOrOTAdmin(), contactInformation.getSmsBlacklisted(), contactInformation.getPhoneNumber());
            values.add(USERS_MATCHING_JOB_PHONE_INDEX, formatString(phoneLabel));
        }
        return values.toArray(String[]::new);
    }

    private String formatMatchingRatePercent(int matchingRate) {
        if (matchingRate > 90) {
            return "Très forte";
        }
        if (matchingRate > 85) {
            return "Forte";
        }
        if (matchingRate > 80) {
            return "Normale";
        }
        if (matchingRate > 70) {
            return "Faible";
        }
        if (matchingRate > 50) {
            return "Très faible";
        }
        return "Aucune";
    }

    private String formatTimestamp(OffsetDateTime creationDate) {
        return creationDate == null ? "" : StringUtils.formatTimestamp(creationDate.toInstant().toEpochMilli());
    }

    private String joinOrganizationTitles(Map<String, String> organizationTitleForCode, List<String> channels) {
        return channels == null ? "" : channels.stream()
                .map(g -> organizationTitleForCode.getOrDefault(g, g))
                .sorted()
                .collect(Collectors.joining(", "));
    }

    private String formatString(String str) {
        return Strings.isNullOrEmpty(str) ? UNDEFINED_LABEL : str;
    }

    private String formatUserProgressProfile(UserProfileProgressDTO dto) {
        var experiencesCount = dto.getExperiencesCount();
        var capacitiesCount = dto.getCapacitiesCount();
        if (capacitiesCount >= 10 && experiencesCount >= 1) {
            return "Correct";
        }
        if (capacitiesCount < 10 || experiencesCount == 0 || dto.getHasBehaviors() != Boolean.TRUE) {
            return "Faible";
        }
        return "Méconnu";
    }

    private String formatSituation(SituationDTO dto) {
        return Optional.ofNullable(dto)
                .map(x -> Situation.valueOf(x.name()))
                .map(Situation::getText)
                .orElse(UNDEFINED_LABEL);
    }

    private String formatSalary(Integer salary) {
        return Optional.ofNullable(salary)
                .map(x -> "%d€".formatted(salary))
                .orElse(UNDEFINED_LABEL);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    public void clearCaches() {
        keycloakService.clearCaches();
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public UserProfileProgressDTO getUserProfileProgress(String userId, String organizationCode) {
        return userProfileRepository
                .getUserProfilesProgress(
                        Set.of(userId),
                        Strings.isNullOrEmpty(organizationCode) ? Collections.emptySet() : keycloakService.getRolesForGroup(organizationCode),
                        securityService.isAdmin(),
                        false,
                        null)
                .stream()
                .map(UserProfileDTOBuilder::buildUserProfileProgress)
                .findFirst()
                .orElseThrow();
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public UserRegistrationStateDTO getUserRegistrationState(String userId) {
        var userProfile = getUserProfileOrThrow(userId);
        return UserProfileDTOBuilder.buildUserRegistrationState(userProfile);
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void setUserOccupation(SetUserOccupationCommandDTO dto) {
        var userProfile = userProfileProvider.getAuthenticatedUserProfileOrCreate();
        ErhgoOccupation occupation = null;
        if (dto.getOccupationId() != null) {
            occupation = erhgoOccupationRepository.findById(dto.getOccupationId())
                    .orElseThrow(() -> new EntityNotFoundException(dto.getOccupationId(), ErhgoOccupation.class));
        }
        if (occupation == null) {
            var result = erhgoOccupationImporterService.createOrUpdateOccupation(dto.getTitle(), OccupationCreationSourceType.FROM_FRONT_OFFICE);
            if (!result.inError()) {
                occupation = erhgoOccupationRepository.findById(result.uuid()).orElseThrow(() -> new EntityNotFoundException(result.uuid(), ErhgoOccupation.class));
            } else {
                throw new InvalidJobTitleException(dto.getTitle());
            }
        }

        if (occupation != null) {
            userProfile.defineUserExperience(occupation, dto.getTitle());
            EventPublisherUtils.publish(new UserExperienceUpdatedEvent(userProfile));
        }
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void tagNoExperienceForUser() {
        var userProfile = userProfileProvider.getAuthenticatedUserProfileOrCreate();
        userProfile.setTaggedNoExperience(true);
    }


    @RolesAllowed(Role.ODAS_ADMIN)
    public void resendInitialMail(String userId) {
        keycloakService.resetFOPassword(userId);
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public List<UserNoteDTO> getUserNotes(String userId) {
        var userProfile = getUserProfileOrThrow(userId);
        return userProfile.userNotes()
                .stream()
                .filter(this::isAuthenticatedUserAllowedForNote)
                .map(n -> new UserNoteDTO()
                        .id(n.getId())
                        .userId(userProfile.userId())
                        .text(n.getContent())
                        .createdBy(keycloakService.getBackOfficeUserFullnameOrEmpty(n.getCreatedByUserId()))
                        .modifiedBy(keycloakService.getBackOfficeUserFullnameOrEmpty(n.getCreatedByUserId()))
                        .createdDate(Optional.ofNullable(n.getCreatedDate()).map(i -> i.toInstant().atOffset(ZoneOffset.UTC)).orElse(null))
                        .modificationDate(Optional.ofNullable(n.getUpdatedDate()).map(i -> i.toInstant().atOffset(ZoneOffset.UTC)).orElse(null))
                        .organizationId(n.getOrganization() == null ? null : n.getOrganization().getId())
                ).toList();
    }

    private boolean isAuthenticatedUserAllowedForNote(UserNote userNote) {
        return securityService.isAdmin() || (userNote.getOrganization() != null && securityService.hasAuthenticatedUserRole(userNote.getOrganization().getCode()));
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_OF_COMMAND_WRITE)
    public void setUserNote(SaveUserNoteCommandDTO command) {
        var userProfile = getUserProfileOrThrow(command.getUserId());
        var organization = Optional.ofNullable(command.getOrganizationId())
                .flatMap(organizationRepository::findById)
                .orElse(null);
        var note = userProfile.editNote(command.getId(), organization, command.getText());
        if (!isAuthenticatedUserAllowedForNote(note)) {
            throw new UserNotAllowedForEntity(note);
        }
        // Next line to get around a bogus Hibernate behavior: throws `detached entity passed to persist` error on cascading note persistence with a new uuid
        userProfileRepository.save(userProfile);
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_OF_COMMAND_WRITE)
    public void deleteUserNote(DeleteUserNoteCommandDTO command) {
        var userProfile = getUserProfileOrThrow(command.getUserId());
        userProfile.deleteNote(command.getId());
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public SetUserEmailResultDTO setUserEmail(String userId, String email) {
        var userProfile = getUserProfileOrThrow(userId);
        var previousEmail = keycloakService.getFrontOfficeUserProfile(userId).map(UserRepresentation::getEmail);
        var hasEmailChanged = keycloakService.updateFOEmail(userId, email);
        var result = new SetUserEmailResultDTO();
        if (hasEmailChanged) {
            result.setCorrectEmailSuggestion(processVerification(userProfile, email).getSuggestion());
            previousEmail.ifPresent(p -> mailingListService.processFOEmailUpdate(p, email));
        } else {
            userProfile.markEmailAsForced();
        }
        return result.confirmationRequired(userProfile.isEmailConfirmationRequired());
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public List<BehaviorDTO> getUserBehaviors(String userId) {
        return userProfileRepository.findByUserId(userId).map(UserProfile::behaviors)
                .map(b -> b.stream().map(BehaviorDTOBuilder::buildDTO)
                        .sorted(Comparator.comparing(BehaviorDTO::getTitle)).toList())
                .orElse(Collections.<BehaviorDTO>emptyList());
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void updateUserBehaviors(String userId, List<UUID> behaviors) {
        getUserProfileOrThrow(userId)
                .updateBehaviors(behaviorRepository.findAllById(behaviors));
    }

    public Boolean getJobOffersOptIn(String userId) {
        return BooleanUtils.negate(getUserProfileOrThrow(userId).getJobOfferOptOut());
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void updateJobOffersOptIn(String userId, Boolean value) {
        var userProfile = getUserProfileOrThrow(userId);
        userProfile.setJobOfferOptOut(BooleanUtils.negate(value));
        EventPublisherUtils.publish(new UserJobOffersOptInUpdatedEvent(userProfile));
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_OF_COMMAND_WRITE)
    public void setUserBlacklistOccupations(SetUserBlacklistOccupationsCommandDTO command) {
        var userProfile = getUserProfileOrThrow(command.getUserId());
        var occupation = erhgoOccupationRepository.findById(UUID.fromString(command.getOccupationId()))
                .orElseThrow(() -> new EntityNotFoundException(command.getOccupationId(), ErhgoOccupation.class));
        userProfile.addOccupationToBlacklist(occupation);
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_OF_COMMAND_WRITE)
    public void removeUserBlacklistOccupations(RemoveUserBlacklistOccupationsCommandDTO command) {
        var userProfile = getUserProfileOrThrow(command.getUserId());
        var occupation = erhgoOccupationRepository.findById(command.getOccupationId())
                .orElseThrow(() -> new EntityNotFoundException(command.getOccupationId(), ErhgoOccupation.class));
        userProfile.removeOccupationToBlacklist(occupation);
    }


    private ChannelAffectationItemDTO buildChannelAffectationItem(ChannelAffectation affectation, UserProfile user) {
        var organization = organizationRepository.findOneByCode(affectation.channel());
        var prescriber = user.prescriber();

        return new ChannelAffectationItemDTO()
                .id(affectation.id())
                .affectedAt(DateTimeUtils.dateToLocalDate(affectation.getCreatedDate()))
                .channelSourceType(affectation.channelSourceType().name())
                .organizationCode(affectation.channel())
                .organizationName(organization != null ? organization.getTitle() : null)
                .prescriberInfo(new PrescriberInfoDTO()
                        .isUserAffectedToChannel(user.isPrescriberInAffectationsHistory())
                        .isThePrescriber(Objects.equals(affectation.channel(), prescriber.channel()))
                );
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public ChannelAffectationInformationsDTO getUserChannelAffectations(String userId) {
        var user = getUserProfileOrThrow(userId);
        var allAffectationsDTO = new ArrayList<>(user
                .affectationsHistory().stream().map(a -> buildChannelAffectationItem(a, user))
                .toList());
        var prescriber = user.prescriber();
        var organization = organizationRepository
                .findOneByCode(user.prescriber().channel());

        return new ChannelAffectationInformationsDTO()
                .userChannelAffectations(allAffectationsDTO)
                .prescriber(new PrescriberDTO()
                        .channelSourceType(prescriber.channelSourceType().name())
                        .organizationCode(prescriber.channel())
                        .organizationName(Optional.ofNullable(organization)
                                .map(AbstractOrganization::getTitle)
                                .orElse(null))
                );
    }

    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    @Transactional(readOnly = true)
    public Boolean getJobDatingOptIn(String userId) {
        return mailingListService.getJobDatingOptIn(getUserProfileOrThrow(userId));
    }

    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    @Transactional(readOnly = true)
    public void updateNewsOptIn(String userId, Boolean value) {
        mailingListService.updateNewsOptIn(getUserProfileOrThrow(userId), value);
    }

    @RolesAllowed(Role.CANDIDATE)
    public void setUserName(SaveUserNameCommandDTO command) {
        var userId = securityService.getAuthenticatedUserId();
        keycloakService.setFrontOfficeUserName(userId, command);
        var userProfile = getUserProfileOrThrow(userId);
        EventPublisherUtils.publish(new UserNameUpdatedEvent(userProfile));
    }

    @Transactional
    public void updateTrimojiPdffUrl(String userId, String url) {
        userProfileRepository.setSoftSkillsUrl(userId, url);
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public int getUserProfileCompletion() {
        var userId = securityService.getAuthenticatedUserId();
        var userProfile = getUserProfileOrThrow(userId);
        var userRepresentation = keycloakService.getFrontOfficeUserProfile(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserRepresentation.class));
        var incompleteInfos = userProfileDTOBuilder.buildIncompleteInformations(userProfile, userRepresentation).size();

        var completeInfosLength = USER_PROFILE_COMPLETION_INCOMPLETE_INFOS_MAX - incompleteInfos;

        return completeInfosLength == 0
                ? USER_PROFILE_COMPLETION_BASE_PERCENTAGE
                : USER_PROFILE_COMPLETION_BASE_PERCENTAGE + completeInfosLength * 5;
    }

    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    @Transactional(readOnly = true)
    public Boolean getHandicapModeEnabled(String userId) {
        return getUserProfileOrThrow(userId).handicapModeEnabled();
    }

    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    @Transactional
    public void updateHandicapModeEnabled(String userId, Boolean value) {
        getUserProfileOrThrow(userId).handicapModeEnabled(value);
    }
}

