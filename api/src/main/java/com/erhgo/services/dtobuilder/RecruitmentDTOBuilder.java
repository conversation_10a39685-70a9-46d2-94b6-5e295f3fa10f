package com.erhgo.services.dtobuilder;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.utils.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

@RequiredArgsConstructor
@Service
public class RecruitmentDTOBuilder {
    private final JobDTOBuilder jobDTOBuilder;
    private final ModelMapper modelMapper;
    private final ErhgoClassificationDTOBuilder erhgoClassificationDTOBuilder;
    private final ErhgoOccupationDataDTOBuilder erhgoOccupationDTOBuilder;
    private final CriteriaDTOBuilder criteriaDTOBuilder;

    public RecruitmentDTO build(Recruitment recruitment) {
        return new RecruitmentDTO()
                .id(recruitment.getId())
                .code(recruitment.getCode())
                .title(recruitment.getTitle())
                .description(recruitment.getDescription())
                .typeContract(recruitment.getTypeContract() == null ? TypeContractDTO.CDI : TypeContractDTO.valueOf(recruitment.getTypeContract().name()))
                .workContractDuration(recruitment.getWorkContractDuration())
                .workContractDurationUnit(recruitment.getWorkContractDurationUnit() == null ? null : WorkContractDurationUnitDTO.valueOf(recruitment.getWorkContractDurationUnit().name()))
                .workingWeeklyTime(recruitment.getWorkingWeeklyTime())
                .location(recruitment.getLocation() == null ? null : recruitment.getLocation().buildDTO())
                .baseSalary(recruitment.getBaseSalary())
                .maxSalary(recruitment.getMaxSalary())
                .hideSalary(recruitment.getHideSalary())
                .externalUrl(recruitment.getUrl())
                .state(recruitment.getState() == null ? null : RecruitmentStateDTO.valueOf(recruitment.getState().name()))
                .startingDate(recruitment.getStartingDate())
                .organizationCode(recruitment.getRecruiterCode())
                .organizationName(recruitment.getOrganizationName())
                .organizationDescription(recruitment.getOrganizationOrRecruiterDescription())
                .recruitmentProfile(RecruitmentProfileDTOBuilder.buildSummary(recruitment.getRecruitmentProfile()))
                .job(jobDTOBuilder.buildSummary(recruitment.getJob()))
                .usersIdToNotify(recruitment.getUsersIdToNotify().stream().toList())
                ;
    }

    public static RecruitmentForMatchingDTO buildForMatching(Recruitment recruitment) {

        var profile = recruitment.getRecruitmentProfile();

        var mandatoryActivities = profile.getMandatoryActivities().stream()
                .map(ActivityDTOBuilder::buildSummary)
                .toList();

        var mandatoryContexts = profile.getMandatoryContexts().stream()
                .map(ContextDTOBuilder::buildSummary)
                .toList();

        List<OptionalActivityDTO> optionalActivities = profile.getOptionalActivities() == null ? Collections.emptyList() :
                profile.getOptionalActivities().stream().map(a ->
                                new OptionalActivityDTO()
                                        .acquisitionModality(AcquisitionModalityDTO.fromValue(a.getAcquisitionModality().name()))
                                        .activity(ActivityDTOBuilder.buildSummary(a.getActivityLabel())))
                        .toList();

        List<OptionalContextDTO> optionalContexts = profile.getOptionalContexts() == null ? Collections.emptyList() :
                profile.getOptionalContexts().stream().map(a ->
                                new OptionalContextDTO()
                                        .acquisitionModality(AcquisitionModalityDTO.fromValue(a.getAcquisitionModality().name()))
                                        .context(ContextDTOBuilder.buildSummary(a.getContext())))
                        .toList();

        List<MissionSummaryDTO> missions = recruitment.getJob().getMissions().stream().map(m -> new MissionSummaryDTO().id(m.getId()).title(m.getTitle())).toList();

        return new RecruitmentForMatchingDTO()
                .id(recruitment.getId())
                .code(recruitment.getCode())
                .jobId(profile.getJob().getId())
                .profileTitle(profile.getTitle())
                .jobTitle(profile.getJob().getTitle())
                .title(recruitment.getTitle())
                .mandatoryActivities(mandatoryActivities)
                .mandatoryContexts(mandatoryContexts)
                .optionalActivities(optionalActivities)
                .optionalContexts(optionalContexts)
                .state(RecruitmentStateDTO.valueOf(recruitment.getState().name()))
                .missions(missions)
                .defaultDiffusionType(Optional.ofNullable(recruitment.getRecruiter().getDefaultDiffusionType()).map(Enum::name).map(DiffusionTypeDTO::valueOf).orElse(DiffusionTypeDTO.BOTH))
                .diffusionType(Optional.ofNullable(recruitment.getDiffusionType()).map(Enum::name).map(DiffusionTypeDTO::valueOf).orElse(null))
                ;
    }

    public RecruitmentDetailDTO buildDetail(Recruitment recruitment) {
        var job = recruitment.getJob();
        var occupation = recruitment.getErhgoOccupation() == null ? null : recruitment.getErhgoOccupation();
        return new RecruitmentDetailDTO()
                .jobTitle(job.getTitle())
                .city(recruitment.getLocation() == null ? null : recruitment.getLocation().getCity())
                .zipCode(recruitment.getLocation() == null ? null : recruitment.getLocation().getPostcode())
                .organizationName(recruitment.getOrganizationName())
                .publicationDate(recruitment.getPublicationDate() != null ? OffsetDateTime.ofInstant(recruitment.getPublicationDate().toInstant(), ZoneId.systemDefault()) : null)
                .publicationEndDate(recruitment.getPublicationEndDate())
                .typeContract(recruitment.getTypeContract() != null ? TypeContractDTO.fromValue(recruitment.getTypeContract().name()) : null)
                .workContractDuration(recruitment.getWorkContractDuration())
                .workContractDurationUnit(recruitment.getWorkContractDurationUnit() != null ? WorkContractDurationUnitDTO.fromValue(recruitment.getWorkContractDurationUnit().name()) : null)
                .workingTimes(buildWorkingTimes(recruitment))
                .workingWeeklyTime(recruitment.getWorkingWeeklyTime())
                .baseSalary(recruitment.getHideSalary() != null && recruitment.getHideSalary() ? null : recruitment.getBaseSalary())
                .maxSalary(recruitment.getHideSalary() != null && recruitment.getHideSalary() ? null : recruitment.getMaxSalary())
                .externalUrl(recruitment.getUrl())
                .description(recruitment.getRecruitmentOrOccupationDescription())
                .organizationDescription(recruitment.getOrganizationOrRecruiterDescription())
                .code(recruitment.getCode())
                .id(recruitment.getId())
                .radiusInKm(recruitment.getLocation() == null ? null : recruitment.getLocation().getRadiusInKm())
                .classifications(recruitment.getErhgoClassifications().stream().map(erhgoClassificationDTOBuilder::buildErhgoClassificationDTO).toList())
                .occupation(occupation == null ? null : erhgoOccupationDTOBuilder.buildOccupationDTOWithoutState(occupation))
                .criteriaValues(job.getCriteriaValues().stream().filter(Predicate.not(CriteriaValue::isRelatedToContractOrWorkingTime)).map(criteriaDTOBuilder::buildCriteriaValueDTO).toList())
                .state(RecruitmentStateDTO.fromValue(recruitment.getState().name()))
                .startingDate(recruitment.getStartingDate())
                ;
    }

    private static List<WorkingTimeDTO> buildWorkingTimes(Recruitment recruitment) {
        return recruitment.getJob()
                .getWorkingTimes()
                .stream()
                .map(TypeWorkingTime::name)
                .map(WorkingTimeDTO::fromValue)
                .toList();
    }

    private static OffsetDateTime convertDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime();
    }

    public static RecruitmentSummaryDTO buildSummary(
            RecruitmentRepository.RecruitmentWithCandidatureCount r,
            String employerTitle,
            String recruiterTitle,
            String sourcingInvitationHostTitle,
            int emailNotifications,
            int mobileNotifications
    ) {
        var recruitment = r.getRecruitment();
        var job = recruitment.getJob();
        var date = DateTimeUtils.instantToOffsetDateTime(recruitment.getSendNotificationDate());
        var state = recruitment.getSendNotificationState() != null ? recruitment.getSendNotificationState() : null;
        return new RecruitmentSummaryDTO()
                .code(recruitment.getCode())
                .id(recruitment.getId())
                .jobTitle(job.getTitle())
                .state(RecruitmentStateDTO.fromValue(recruitment.getState().name()))
                .profileTitle(recruitment.getRecruitmentProfile().getTitle())
                .employerTitle(employerTitle)
                .recruiterTitle(recruiterTitle)
                .city(recruitment.getLocation() == null ? null : recruitment.getLocation().getCity())
                .candidaturesCount(new RecruitmentSummaryCandidaturesCountDTO()
                        .newCandidatureCount(new CandidatureCountItemDTO()
                                .fromUser(r.getNewFromUser())
                                .generated(r.getNewGenerated())
                        )
                        .totalCandidatureCount(new CandidatureCountItemDTO()
                                .fromUser(r.getTotalFromUser())
                                .generated(r.getTotalGenerated())
                        )
                        .contactedCandidatureCount(r.getContactedCandidatureCount())
                        .toContactCandidatureCount(r.getToContactCandidatureCount())
                        .newMatchingCandidatureCount(r.getNewMatchingCandidatureCount())
                        .refusedCandidatureCount(r.getRefusedCandidatureCount())
                )
                .lastProcessingDate(r.getLastProcessingDate())
                .lastProcessingType(r.getLastProcessingType() == null ? null : ProcessingTypeDTO.valueOf(recruitment.getLastProcessingType().name()))
                .recruiterCode(recruitment.getRecruiterCode())
                .publicationDate(convertDate(recruitment.getPublicationDate()))
                .sourcingHost(sourcingInvitationHostTitle)
                .sendNotificationDate(date)
                .sendNotificationState(state != null ? RecruitmentSummaryDTO.SendNotificationStateEnum.valueOf(state.name()) : null)
                .mailNotificationCount(emailNotifications)
                .mobileNotificationsCount(mobileNotifications);

    }

    public static RecruitmentSummaryForCandidateDTO buildSummaryForCandidate(
            Recruitment recruitment,
            Boolean hasApplied,
            Boolean isRefused,
            Float distance
    ) {
        return new RecruitmentSummaryForCandidateDTO()
                .code(recruitment.getCode())
                .id(recruitment.getId())
                .profileTitle(recruitment.getRecruitmentProfile().getTitle())
                .city(recruitment.getLocation() == null ? null : recruitment.getLocation().getCity())
                .hasApplied(hasApplied == Boolean.TRUE)
                .isRefused(isRefused)
                .distance(distance)
                .radiusInKm(recruitment.getLocation() == null ? null : recruitment.getLocation().getRadiusInKm());
    }

    public SimpleRecruitmentSummaryDTO buildRecruitmentSummary(
            RecruitmentRepository.SimpleRecruitment recruitment) {
        return new SimpleRecruitmentSummaryDTO()
                .code(recruitment.getCode())
                .organizationName(recruitment.getOrganizationName())
                .jobTitle(recruitment.getJobTitle())
                .city(recruitment.getCity())
                .baseSalary(recruitment.getHideSalary() == Boolean.TRUE ? null : recruitment.getBaseSalary())
                .maxSalary(recruitment.getHideSalary() == Boolean.TRUE ? null : recruitment.getMaxSalary())
                .typeContract(TypeContractDTO.fromValue(ContractType.values()[recruitment.getTypeContract()].name()));
    }

    public RecruitmentSumUpDTO buildSumUp(Recruitment recruitment) {
        return new RecruitmentSumUpDTO()
                .recruitmentId(recruitment.getId())
                .recruitmentState(recruitment.getState() == null ? null : RecruitmentStateDTO.valueOf(recruitment.getState().name()))
                .location(recruitment.getLocation() == null ? null : recruitment.getLocation().buildDTO())
                .baseSalary(recruitment.getBaseSalary())
                .maxSalary(recruitment.getMaxSalary())
                .title(recruitment.getJobTitle())
                .customQuestion(recruitment.getCustomQuestion())
                .publicationDate(convertDate(recruitment.getPublicationDate()))
                ;
    }
}
