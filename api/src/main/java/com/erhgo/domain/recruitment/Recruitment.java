package com.erhgo.domain.recruitment;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.exceptions.UserNotAllowedForEntity;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingSubscription;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.utils.EventPublisherUtils;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
@Entity
@NoArgsConstructor
@ToString(exclude = {"erhgoClassifications", "usersIdToNotify", "publicationEndDate", "sourcingUsersIdToNotify", "externalOffer"})
@EqualsAndHashCode(callSuper = true, exclude = {
        "usersIdToNotify", "publicationEndDate", "sourcingUsersIdToNotify",
        "lastProcessingType", "lastProcessingDate", "sendNotificationDate", "sendNotificationState", "erhgoClassifications"
})
@AllArgsConstructor
public class Recruitment extends AbstractAuditableEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Setter
    private String code;

    private String title;

    @Column(columnDefinition = "LONGTEXT")
    private String description = "";

    @Column
    private ContractType typeContract;
    private Integer workContractDuration;
    private WorkContractDurationUnit workContractDurationUnit;

    private Integer workingWeeklyTime;
    private Boolean modularWorkingTime;
    @Embedded
    private Location location;

    private Integer baseSalary;
    private Integer maxSalary;

    private Boolean hideSalary;

    @Getter(AccessLevel.PRIVATE)
    private String externalUrl;

    @Column(nullable = false)
    private RecruitmentState state;

    private Integer sourcingStep;
    private OffsetDateTime startingDate;
    private Date publicationDate;
    private OffsetDateTime publicationEndDate;

    @Column(columnDefinition = "LONGTEXT")
    @Getter(AccessLevel.PRIVATE)
    private String organizationDescription;

    @ManyToOne(optional = false)
    private RecruitmentProfile recruitmentProfile;

    @ElementCollection
    private Set<String> usersIdToNotify = new HashSet<>();

    @ElementCollection
    private Set<String> sourcingUsersIdToNotify = new HashSet<>();

    @ManyToMany
    private Set<ErhgoClassification> erhgoClassifications = new HashSet<>();

    @Enumerated(EnumType.STRING)
    private RecruitmentSendNotificationState sendNotificationState;

    private Instant sendNotificationDate;

    private OffsetDateTime lastProcessingDate;

    @Enumerated(EnumType.STRING)
    private ProcessingType lastProcessingType;

    private String managerUserId;

    private String source;

    @OneToOne(mappedBy = "recruitment")
    private ExternalOffer externalOffer;

    private Long bitMaskCA1;
    private Long bitMaskCA2;
    private Long bitMaskCA3;

    @Enumerated(EnumType.STRING)
    private DiffusionType diffusionType;

    public Set<String> getKeywords() {
        return Optional.ofNullable(getErhgoClassifications())
                .orElse(Collections.emptySet())
                .stream()
                .map(ErhgoClassification::getTitle)
                .collect(Collectors.toSet());
    }

    public Set<String> getOccupationAlternativeLabels() {
        return Optional.ofNullable(getErhgoOccupation()).map(ErhgoOccupation::getAlternativeLabels).orElse(Collections.emptySet());
    }

    public Set<String> getActivitiesLabels() {
        return recruitmentProfile.getActivities().stream().map(JobActivityLabel::getTitle).collect(Collectors.toSet());
    }

    public Set<CriteriaValue> getCriteriaValues() {
        return getJob().getCriteriaValues();
    }

    public String getPostcode() {
        return Optional.ofNullable(location).map(Location::getPostcode).orElse("");
    }

    public String getCity() {
        return Optional.ofNullable(location).map(Location::getCity).orElse("");
    }

    public String getAtsCode() {
        return Optional.ofNullable(getExternalOffer()).map(ExternalOffer::getAtsCode).orElse("");
    }

    public enum ProcessingType {
        COMMENT,
        PUBLISH,
        CLOSE,
        REPUBLISH,
        SUSPEND,
        REFUSE_CANDIDATURE,
        FAV_CANDIDATURE,
        MEET_CANDIDATURE,
        MEET_LATER_CANDIDATURE
    }

    @Builder
    public Recruitment(String title,
                       ContractType typeContract,
                       RecruitmentState state,
                       RecruitmentProfile recruitmentProfile,
                       Integer sourcingStep,
                       Set<String> sourcingUsersIdToNotify,
                       String managerUserId
    ) {
        this.title = title;
        this.typeContract = typeContract;
        this.state = state;
        this.sourcingStep = sourcingStep;
        this.recruitmentProfile = recruitmentProfile;
        this.sourcingUsersIdToNotify = sourcingUsersIdToNotify == null ? new HashSet<>() : sourcingUsersIdToNotify;
        this.managerUserId = managerUserId;
        this.source = "MANUAL";
        refreshClassificationsUsingOccupation();
    }

    public void updateLastProcessingData(ProcessingType type) {
        this.lastProcessingDate = OffsetDateTime.now();
        this.lastProcessingType = type;
    }

    public void refreshClassificationsUsingOccupation() {
        if (erhgoClassifications == null) {
            erhgoClassifications = new HashSet<>();
        }
        if (recruitmentProfile != null && getErhgoOccupation() != null) {
            erhgoClassifications.removeIf(e -> getErhgoOccupation().getErhgoClassifications().stream().noneMatch(c -> e.getCode().equals(c.getCode())));
            erhgoClassifications.addAll(getErhgoOccupation().getErhgoClassifications());
        } else {
            erhgoClassifications.clear();
        }
    }


    public boolean isCandidatureReceivable() {
        return state == RecruitmentState.PUBLISHED;
    }

    public Job getJob() {
        return recruitmentProfile == null ? null : recruitmentProfile.getJob();
    }

    public boolean isSelectableForCandidatureGeneration() {
        return state == RecruitmentState.PUBLISHED;
    }

    public String getEmployerCode() {
        return getJob() == null ? null : getJob().getEmployerCode();
    }

    public void updateCodeOnJobCreate() {
        this.code = "R-" + id;
    }

    public String getOrganizationName() {
        return getJob().getOrganizationName();
    }

    public String getRecruiterCode() {
        return getJob().getRecruiterCode();
    }

    @Deprecated //Use getRecruiterCode() instead
    public String getOrganizationCode() {
        return getJob().getRecruiterCode();
    }

    public boolean isSourcing() {
        return getJob().getRecruiter().getOrganizationType() == AbstractOrganization.OrganizationType.SOURCING;
    }

    public ErhgoOccupation getErhgoOccupation() {
        return getJob().getErhgoOccupation();
    }

    public UUID getErhgoOccupationId() {
        return Optional.ofNullable(getErhgoOccupation()).map(ErhgoOccupation::getId).orElse(null);
    }

    public String getRecruitmentOrOccupationDescription() {
        return Optional.ofNullable(Strings.trimToNull(description)).orElse(Optional.ofNullable(getErhgoOccupation()).map(ErhgoOccupation::getDescriptionWithBehaviorDescription).orElse(""));
    }

    public String getOrganizationOrRecruiterDescription() {
        return Optional.ofNullable(Strings.trimToNull(organizationDescription)).orElse(Strings.trimToNull(getRecruiter().getDescription()));
    }

    public void clearErhgoClassifications() {
        this.erhgoClassifications.clear();
    }


    public void resetErhgoClassifications(Collection<ErhgoClassification> erhgoClassifications) {
        this.erhgoClassifications.removeIf(ec -> !erhgoClassifications.contains(ec));
        this.erhgoClassifications.addAll(erhgoClassifications);
    }

    public Recruitment setState(RecruitmentState nextState, boolean causedBySystem) {
        var previousState = getState();
        if (!causedBySystem) {
            this.updateLastProcessingData(getActionType(nextState));
        }
        this.state = nextState;
        if (nextState != RecruitmentState.PUBLISHED && sendNotificationState == RecruitmentSendNotificationState.WAITING) {
            this.sendNotificationState = RecruitmentSendNotificationState.CANCEL;
            this.sendNotificationDate = null;
        }

        if (nextState != RecruitmentState.PUBLISHED && nextState != RecruitmentState.DRAFT && (publicationEndDate == null || publicationEndDate.isAfter(OffsetDateTime.now()))) {
            publicationEndDate = OffsetDateTime.now();
        }

        EventPublisherUtils.publish(new RecruitmentStateChangeEvent(id, state, previousState));
        return this;
    }

    private ProcessingType getActionType(RecruitmentState nextState) {
        switch (nextState) {
            case PUBLISHED -> {
                return this.state == RecruitmentState.DRAFT ? null : ProcessingType.REPUBLISH;
            }
            case UNPUBLISHED, SELECTION -> {
                return ProcessingType.SUSPEND;
            }
            case CLOSED -> {
                return ProcessingType.CLOSE;
            }
            default -> {
                return null;
            }
        }
    }

    public boolean publish() {
        var stateChanged = state != RecruitmentState.PUBLISHED;
        setPublicationDate(new Date());
        setState(RecruitmentState.PUBLISHED, false);
        getJob().publish();
        return stateChanged;
    }

    public void publishSourcingRecruitment(
            SourcingSubscription subscription,
            int trialDurationInDays,
            boolean requiresNotificationsSend,
            int recruitmentLifespanInDays,
            boolean allowRepublicationOfAlreadyPublishedRecruitment) {
        refreshBitmasks();
        var stateChanged = publish();
        if (!stateChanged && !allowRepublicationOfAlreadyPublishedRecruitment) {
            throw new UserNotAllowedForEntity("Recruitment %d is published and should not be re-published".formatted(id));
        }
        Optional.ofNullable(subscription)
                .filter(s -> s.isActiveTrial() && s.getExpirationDate() == null)
                .ifPresent(s -> s.setExpirationDate(OffsetDateTime.now().plusDays(trialDurationInDays)));
        if (relatesToExternalOffer()) {
            this.publicationEndDate = null;
        } else {
            this.publicationEndDate = OffsetDateTime.now().plusDays(recruitmentLifespanInDays);
        }
        sendNotificationState = RecruitmentSendNotificationState.CANCEL;

        if (requiresNotificationsSend) {
            sendNotificationDate = Instant.now();
            sendNotificationState = RecruitmentSendNotificationState.WAITING;
        }
    }

    public String getRecruiterTitle() {
        return getJob().getRecruiterTitle();
    }

    public AbstractOrganization.OrganizationType getRecruiterType() {
        return getRecruiter().getOrganizationType();
    }

    public Recruitment changeSendNotificationsState(RecruitmentSendNotificationState nextState) {
        this.sendNotificationState = nextState;
        this.sendNotificationDate = null;
        return this;
    }

    public Recruiter getRecruiter() {
        return getJob().getRecruiter();
    }

    public String getJobTitle() {
        return getJob().getTitle();
    }

    public String getCustomQuestion() {
        return getRecruitmentProfile().getCustomQuestion();
    }

    public boolean isClosed() {
        return state == RecruitmentState.CLOSED;
    }

    public Recruitment duplicates(RecruitmentProfile duplicatedRecruitmentProfile, String authenticatedUserId) {
        var recruitment = Recruitment.builder()
                .recruitmentProfile(duplicatedRecruitmentProfile)
                .sourcingStep(0)
                .state(RecruitmentState.DRAFT)
                .typeContract(getTypeContract())
                .sourcingUsersIdToNotify(Set.of(authenticatedUserId))
                .build();
        recruitment.title = title;
        recruitment.description = description;
        recruitment.workContractDuration = workContractDuration;
        recruitment.workContractDurationUnit = workContractDurationUnit;
        recruitment.workingWeeklyTime = workingWeeklyTime;
        recruitment.modularWorkingTime = modularWorkingTime;
        recruitment.location = Optional.ofNullable(location).map(Location::duplicates).orElse(null);
        recruitment.baseSalary = baseSalary;
        recruitment.maxSalary = maxSalary;
        recruitment.hideSalary = hideSalary;
        recruitment.externalUrl = externalUrl;
        recruitment.organizationDescription = organizationDescription;
        recruitment.erhgoClassifications = new HashSet<>(erhgoClassifications);
        recruitment.sendNotificationState = sendNotificationState;
        recruitment.sendNotificationDate = sendNotificationDate;
        recruitment.managerUserId = authenticatedUserId;
        recruitment.source = source;
        return recruitment;
    }

    public String getUrl() {
        return (StringUtils.isBlank(externalUrl) || getRecruiter().isForcedUrl()) ? getRecruiter().getUrl() : externalUrl;
    }

    public DiffusionType getEffectiveDiffusionType() {
        return Optional.ofNullable(diffusionType).orElse(getRecruiter().getDefaultDiffusionType());
    }

    public void setJobTitle(String newTitleParam) {
        var newTitle = newTitleParam.trim();
        title = title == null ? newTitleParam : title.replace(getJobTitle(), newTitle);
        getJob().setTitle(newTitle);
    }

    public boolean relatesToExternalOffer() {
        return this.externalOffer != null;
    }

    public void refreshBitmasks() {
        var bitmasksPerLevel = Capacity.buildBitmaskPerLevelForCapacities(getJob().getAllCapacities());
        bitMaskCA1 = bitmasksPerLevel.get(1);
        bitMaskCA1 = bitmasksPerLevel.get(2);
        bitMaskCA1 = bitmasksPerLevel.get(3);
    }

    public void setSourcingUsersIdToNotify(Set<String> sourcingUsersIdToNotify) {
        var currentUsers = new HashSet<>(this.sourcingUsersIdToNotify);
        currentUsers.removeIf(id -> !sourcingUsersIdToNotify.contains(id));
        currentUsers.addAll(sourcingUsersIdToNotify);
        this.sourcingUsersIdToNotify = currentUsers;
    }

    public Recruitment setStartingDate(OffsetDateTime startingDate) {
        this.startingDate = startingDate;
        this.sendNotificationDate = Optional.ofNullable(startingDate)
                .map(OffsetDateTime::toInstant)
                .orElse(Instant.now());
        return this;
    }
}
