package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.OrganizationApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.OrganizationService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class OrganizationController implements OrganizationApi {
    private final OrganizationService service;

    @Override
    public ResponseEntity<Void> saveOrganization(SaveOrganizationCommandDTO saveOrganizationCommandDTO) {
        service.save(saveOrganizationCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<OrganizationDTO> getOrganization(Long id) {
        return ResponseEntity.ok(service.getOrganization(id));
    }


    @Override
    public ResponseEntity<List<OrganizationSummaryDTO>> getAllRecruitersForOrganization(String organizationCode) {
        return ResponseEntity.ok(service.getAllRecruitersForOrganization(organizationCode));
    }

    @Override
    public ResponseEntity<List<OrganizationSummaryDTO>> getAllRecruiters() {
        return ResponseEntity.ok(service.getAllRecruiters());
    }


    @Override
    public ResponseEntity<OrganizationPageDTO> findOrganizationsPaginatedAndFilteredByProperty(Integer page, Integer size, String by, SortDirectionDTO direction, String filter, List<String> refererRecruiters) {
        return ResponseEntity.ok(
                service.findPaginatedAndFilteredByProperty(
                        page,
                        size,
                        by,
                        direction,
                        filter,
                        refererRecruiters));
    }


    @Override
    public ResponseEntity<OrganizationDTO> getOrganizationByCode(String code) {
        return ResponseEntity.ok(service.findOrganizationByCode(code));
    }

    @Override
    public ResponseEntity<Void> reindexRecruiterRecruitments(String recruiterCode) {
        service.reindexRecruiterRecruitments(recruiterCode);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<OrganizationSummaryDTO>> getAllOrganizationsByCodes(List<String> codes) {
        return ResponseEntity.ok(service.findOrganizationsByCodes(codes));
    }
}
