package com.erhgo.config;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

@AllArgsConstructor
public enum EnvironmentProfile {

    MASTER("master"),
    STAGING("staging"),
    TESTING("testing"),
    TEST("test"),
    LOCAL("default");

    @Getter
    private String text;


    public static Optional<EnvironmentProfile> fromText(String profileString) {
        return Arrays.stream(EnvironmentProfile.values())
                .filter(envProfile -> envProfile.getText().equalsIgnoreCase(profileString))
                .findFirst();
    }

    public static String formatProfileForEmailSubject(String string) {
        var environmentProfile = EnvironmentProfile.fromText(string).orElse(null);

        switch (Objects.requireNonNull(environmentProfile)) {
            case STAGING -> {
                return "[Test - STAGING]";
            }
            case TESTING -> {
                return "[Test - TESTING]";
            }
            case TEST, LOCAL -> {
                return "[Test]";
            }
            default -> {
                return null;
            }
        }

    }
}
